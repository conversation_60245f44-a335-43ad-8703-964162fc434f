"""execution table created

Revision ID: f8de8a4184f2
Revises: 0004
Create Date: 2025-07-21 12:44:56.559893

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, JSONB

# revision identifiers, used by Alembic.
revision = 'f8de8a4184f2'
down_revision = '0004'
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')

    op.create_table('workflow_execution',
        sa.Column('id', UUID(), server_default=sa.text('uuid_generate_v4()'), autoincrement=False, nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=False),
        sa.Column('workflow_id', UUID(), autoincrement=False, nullable=False),
        sa.Column('workflow_version_id', sa.Integer(), autoincrement=False, nullable=False),
        sa.Column('status', sa.Enum('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', name='executionstatus'), server_default=sa.text("'PENDING'::executionstatus"), autoincrement=False, nullable=False),
        sa.Column('error_message', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
        sa.Column('duration_seconds', sa.Integer(), autoincrement=False, nullable=True),
        sa.Column('billable_duration_seconds', sa.Integer(), autoincrement=False, nullable=True, index=True),
        sa.Column('result', JSONB(), nullable=True),
        sa.Column('is_test', sa.Boolean(), server_default=sa.text('false'), nullable=False),
        sa.Column('start_time', sa.String(length=50), nullable=True, index=True),
        sa.Column('end_time', sa.String(length=50), nullable=True, index=True),
        sa.Column('created_by', sa.Integer(), autoincrement=False, nullable=True),
        sa.Column('updated_by', sa.Integer(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(['workflow_id'], ['work_flow.id'], name='fk_workflow_execution_workflow_id'),
        sa.ForeignKeyConstraint(['workflow_version_id'], ['work_flow_version.id'], name='fk_workflow_execution_workflow_version_id'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], name='fk_workflow_execution_created_by'),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], name='fk_workflow_execution_updated_by'),
        sa.PrimaryKeyConstraint('id', name='workflow_execution_pkey'),
        postgresql_ignore_search_path=False
    )

    op.create_index(op.f('ix_workflow_execution_workflow_id'), 'workflow_execution', ['workflow_id'], unique=False)
    op.create_index(op.f('ix_workflow_execution_workflow_version_id'), 'workflow_execution', ['workflow_version_id'], unique=False)
    op.create_index(op.f('ix_workflow_execution_created_by'), 'workflow_execution', ['created_by'], unique=False)
    op.create_index(op.f('ix_workflow_execution_updated_by'), 'workflow_execution', ['updated_by'], unique=False)
    op.create_index(op.f('ix_workflow_execution_status'), 'workflow_execution', ['status'], unique=False)
    op.create_index(op.f('ix_workflow_execution_created_at'), 'workflow_execution', ['created_at'], unique=False)
    op.create_index(op.f('ix_workflow_execution_updated_at'), 'workflow_execution', ['updated_at'], unique=False)
    op.create_index(op.f('ix_workflow_execution_id'), 'workflow_execution', ['id'], unique=False)

    


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_workflow_execution_id', table_name='workflow_execution')
    op.drop_index('ix_workflow_execution_updated_at', table_name='workflow_execution')
    op.drop_index('ix_workflow_execution_created_at', table_name='workflow_execution')
    op.drop_index('ix_workflow_execution_updated_by', table_name='workflow_execution')
    op.drop_index('ix_workflow_execution_created_by', table_name='workflow_execution')
    op.drop_index('ix_workflow_execution_status', table_name='workflow_execution')
    op.drop_index('ix_workflow_execution_workflow_version_id', table_name='workflow_execution')
    op.drop_index('ix_workflow_execution_workflow_id', table_name='workflow_execution')
    op.drop_table('workflow_execution')
    # ### end Alembic commands ###
    op.execute("DROP TYPE IF EXISTS executionstatus CASCADE")
