"""
Workflow-related Pydantic schemas for request/response validation.
"""

from typing import Any, Dict, List, Optional
import uuid

from pydantic import Field

from app.schemas.common import BaseSchema
from app.node.node_base.node_models import NodeRequest, WorkflowExecutionStatus, WorkflowModel, WorkflowStatus


class SimplifiedTag(BaseSchema):
    """Simplified tag schema for flattened workflow responses."""

    id: int = Field(..., description="Tag ID")
    name: str = Field(..., description="Tag name")

class WorkFlowDefinition(BaseSchema):
    """Schema for workflow definition with version management."""
    # start_node: Optional[str] = Field(None, description="ID of the starting node for workflow execution")
    nodes: Dict[str, NodeRequest] = Field(description="Dictionary of nodes with node IDs as keys")
    connections: Dict[str, Dict[str, List[List[str]]]] = Field(description="Dictionary of connections with node IDs as keys")
    notes: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="Optional list of notes for the workflow"
    )

    model_config = {
        "json_schema_extra": {
            "example": {
                # "start_node": "wait_1",
                "nodes": [
                    {
                        "id": "start",
                        "type": "trigger",
                        "position": [100, 100],
                        "config": {
                            "webhook_url": "https://example.com/webhook"
                        }
                    },
                    {
                        "id": "process",
                        "type": "action",
                        "position": [300, 100],
                        "config": {
                            "action_type": "email",
                            "template": "welcome_email"
                        }
                    }
                ],
                "connections": [
                    {
                        "from": "start",
                        "to": "process"
                    }
                ],
            }
        }
    }

class WorkFlowCreate(BaseSchema):
    """Schema for creating a new workflow."""

    name: str = Field(..., min_length=1, max_length=255, description="Workflow name")
    is_active: bool = Field(default=True, description="Whether the workflow is active")
    description: Optional[str] = Field(None, description="Workflow description")
    status: WorkflowStatus = Field(default=WorkflowStatus.DRAFT, description="Workflow status")
    tag_ids: Optional[List[int]] = Field(default=[], description="List of tag IDs to associate with the workflow")
    work_flow: Optional[WorkFlowDefinition] = Field(None, description="Workflow definition for creating or updating a version")
    version_name: Optional[str] = Field(None, description="Name for this version")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "name": "Customer Onboarding Workflow",
                "is_active": True,
                "description": "Automated workflow for onboarding new customers",
                "status": "draft",
                "initial_data": {
                    "trigger": "webhook",
                    "environment": "production"
                },
                "tag_ids": [1, 2, 3],
                "version_name": "Initial Version",
                "work_flow": {
                    "work_flow_id": None,
                    "start_node": "wait_1",
                    "version_name": "Initial Version",
                    "nodes": [
                        {
                            "id": "start",
                            "type": "trigger",
                            "position": [100, 100],
                            "config": {
                                "webhook_url": "https://example.com/webhook"
                            }
                        }
                    ],
                    "connections": [],
                }
            }
        }
    }

class WorkFlowUpdate(BaseSchema):
    """Schema for updating an existing workflow."""

    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Workflow name")
    is_active: Optional[bool] = Field(None, description="Whether the workflow is active")
    description: Optional[str] = Field(None, description="Workflow description")
    # status: Optional[WorkflowStatus] = Field(None, description="Workflow status")
    tag_ids: Optional[List[int]] = Field(None, description="List of tag IDs to associate with the workflow")
    work_flow: Optional[WorkFlowDefinition] = Field(None, description="Workflow definition for creating or updating a version")
    version_name: Optional[str] = Field(None, description="Name for this version")

class WorkFlowFlattenedResponse(BaseSchema):
    """Schema for flattened workflow response combining workflow and active version data."""

    # Workflow fields
    created_at: str = Field(..., description="Workflow creation timestamp")
    updated_at: str = Field(..., description="Workflow last update timestamp")
    name: str = Field(..., description="Workflow name")
    is_active: bool = Field(..., description="Whether the workflow is active")
    description: Optional[str] = Field(None, description="Workflow description")
    status: WorkflowStatus = Field(..., description="Workflow status")
    created_by: str = Field(..., description="Name of the user who created this workflow")
    edited_by: Optional[str] = Field(None, description="Name of the user who last edited this workflow")
    id: str = Field(..., description="Unique identifier for the workflow")

    # Simplified tags (only id and name)
    tags: List[SimplifiedTag] = Field(default=[], description="Associated tags with simplified structure")

    # Active version fields (renamed id to active_version_id for clarity)
    published_version_id: Optional[int] = Field(None, description="ID of the published version")
    current_version_id: int = Field(..., description="Active version number")
    version_name: Optional[str] = Field(None, description="Active version name")
    work_flow: Dict[str, Any] = Field(..., description="Complete workflow definition with nodes and connections")

    model_config = {
        "json_schema_extra": {
            "example": {
                "created_at": "2025-07-07T10:37:32.648679Z",
                "updated_at": "2025-07-07T10:37:32.648679Z",
                "name": "My Workflow",
                "is_active": True,
                "created_by": "John Doe",
                "edited_by": None,
                "id": "7f5bcb4c-e442-47c9-82e2-1765cb5113be",
                "tags": [
                    {
                        "id": 1,
                        "name": "production"
                    }
                ],
                "published_version_id": 1,
                "current_version_id": 1,
                "version_name": "V0.0.1",
                "work_flow": {
                    "start_node": "wait_1",
                    "nodes": {
                        "wait_1": {
                            "name": "wait_1",
                            "type": "wait",
                            "version": 1,
                            "position": [-543, 920],
                            "parameters": {
                                "date_time": "2025-06-20T07:44:00.000Z",
                                "wait_unit": "seconds",
                                "wait_value": 15,
                                "resume_type": "specific_time"
                            },
                            "description": "Pauses workflow execution for a specified duration or until a specific time.",
                            "display_name": "Wait"
                        }
                    },
                    "connections": {
                        "wait_1": {
                            "main": [
                                ["wait_2", "wait_3"]
                            ]
                        }
                    },
                    "work_flow_id": None,
                    "version_tag_id": None
                }
            }
        }
    }

class WorkFlowList(BaseSchema):
    """Schema for workflow list response."""

    workflows: List[Any]
    total: int
    page: int
    size: int

    model_config = {
        "json_schema_extra": {
            "example": {
                "workflows": [],
                "total": 0,
                "page": 1,
                "size": 10
            }
        }
    }

class WorkflowExecuteRequest(BaseSchema):
    """Schema for executing a workflow by ID."""
    
    input_data: Optional[Dict[str, Any]] = Field(
        default={}, 
        description="Input data for workflow execution"
    )

    work_flow: Optional[WorkFlowDefinition] = Field(
        default=None,
        description="Workflow definition for execution, including nodes and connections"
    )
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "input_data": {
                    "customer_id": "cus_12345",
                    "event_type": "new_signup",
                    "metadata": {
                        "source": "website",
                        "campaign": "summer_promo"
                    }
                },
                "work_flow": {
                    "nodes": {
                        "start": {
                            "name": "start",
                            "type": "trigger",
                            "display_properties": {},
                            "is_active": True,
                            "is_trigger": True,
                            "parameters": {},
                            "credentials": None
                        },
                        "process": {
                            "name": "process",
                            "type": "action",
                            "display_properties": {},
                            "is_active": True,
                            "is_trigger": False,
                            "parameters": {"action_type": "email"},
                            "credentials": None
                        },
                        # Removed duplicate "process" entry to ensure JSON validity.
                    },
                    "connections": {
                        "start": {"main": [["process"]]}
                    }
                }
            }
        }
    }


class WorkflowExecutionResponse(BaseSchema):
    """Schema for workflow execution response."""
    
    workflow_id: str = Field(..., description="ID of the executed workflow")
    execution_id: str = Field(..., description="ID of the workflow execution")
    workflow_name: str = Field(..., description="Name of the executed workflow")
    status: str = Field(..., description="Status of the workflow execution")
    start_time: Optional[str] = Field(None, description="ISO formatted start time of the execution")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "workflow_id": "123e4567-e89b-12d3-a456-************",
                "execution_id": "workflow-123e4567-e89b-12d3-a456-************-98765",
                "workflow_name": "Customer Onboarding Workflow",
                "status": "RUNNING",
                "start_time": "2025-07-10T15:30:45Z"
            }
        }
    }

class WorkflowExecutionCreate(BaseSchema):
    id: uuid.UUID = Field(..., description="Unique identifier for the workflow execution")
    workflow_id: uuid.UUID = Field(..., description="ID of the workflow to execute")
    workflow_version_id: int = Field(..., description="ID of the workflow version to execute")
    status : Optional[WorkflowExecutionStatus] = Field(
        WorkflowExecutionStatus.RUNNING,
        description="Status of the workflow execution"
    )
    error_message: Optional[str] = Field(None, description="Error message if execution failed")
    result: Optional[WorkflowModel] = Field(
        None,
        description="Result of the workflow execution, if any"
    )
    is_test: Optional[bool] = Field(
        False, 
        description="Whether this execution is a test run"
    )
    created_by: Optional[int] = Field(None, description="ID of the user who created this execution")
    updated_by: Optional[int] = Field(None, description="ID of the user who last updated this execution")

class WorkflowExecutionUpdate(BaseSchema):
    """Schema for updating a workflow execution."""
    
    status: Optional[WorkflowExecutionStatus] = Field(
        default = WorkflowExecutionStatus.PENDING,
        description="Updated status of the workflow execution"
    )
    error_message: Optional[str] = Field(default = None, description="Error message if execution failed")
    duration_seconds: Optional[int] = Field(default = None, description="Duration of the execution in seconds")
    start_time: Optional[str] = Field(default = None, description="ISO formatted start time of the execution")
    end_time: Optional[str] = Field(default = None, description="ISO formatted end time of the execution")
    billable_duration_seconds: Optional[int] = Field(
        default =None, 
        description="Billable duration of the execution in seconds"
    )
    result: Optional[Dict[str, Any]] = Field(
       default= None, 
        description="Result of the workflow execution, if any"
    )
    

