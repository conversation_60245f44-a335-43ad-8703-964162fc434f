from typing import Any, Optional
import uuid
from temporalio import activity
from sqlalchemy.ext.asyncio import AsyncSession

from app.node.node_base.node_models import WorkflowExecutionUpdate

from app.utils.db_util import db_session

@activity.defn(name="state_update_activity")
async def state_update_activity(data: WorkflowExecutionUpdate):
    """
    Activity to update the state of a workflow node.
    
    Args:
        node_data (Any): Data related to the node whose state needs to be updated.
        
    Returns:
        Any: Result of the state update operation.
    """
    try:
        execution_id = data.execution_id
        if not execution_id:
            raise ValueError("Execution ID is required to update state")
        if not data:
            raise ValueError("Data is required to update state")
        id = uuid.UUID(execution_id)
        
        await update_node_state(id, data)
    except Exception as e:
        raise RuntimeError(f"Failed to update state: {str(e)}")
    

@db_session()
async def update_node_state(
    execution_id: uuid.UUID,
    data: WorkflowExecutionUpdate,
    db: Optional[AsyncSession] = None
) -> None:
    """
    Update the state of a specific node in the workflow.
    
    Args:
        node_id (str): The ID of the node to update.
        new_state (Any): The new state to set for the node.
        db (Optional[AsyncSession]): Database session for executing the update.
        
    Raises:
        ValueError: If the database session is not provided.
    """
    if db is None:
        raise ValueError("Database session is required to update node state")
    
    from app.repositories.workflow_execution_repository import WorkflowExecutionRepository
    
    repo = WorkflowExecutionRepository(db)
    execution = await repo.get_by_uid(execution_id)
    if not execution:
        raise ValueError(f"Workflow execution with ID {execution_id} not found")
    
    final_data = data.model_dump(exclude_none=True, exclude_unset=True, exclude={"execution_id"})
    if data.result:
        from sqlalchemy.orm.attributes import flag_modified
        result = execution.result or {}
        if "nodes" not in result:
            result["nodes"] = {}
        for node_name, node_data in data.result.items():
            if node_name not in result["nodes"]:
                result["nodes"][node_name] = {}
            result["nodes"][node_name] = node_data
        final_data["result"] = result
        data.result = result
        flag_modified(execution, "result")

    await repo.update(execution, final_data)
    await db.commit()