"""
Workflow management models for storing workflow definitions and versions.
"""

import enum
import uuid
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Check<PERSON>onstraint, Column, Enum, Foreign<PERSON>ey, Integer, String, Table, UniqueConstraint, func
from sqlalchemy.dialects.postgresql import <PERSON><PERSON>N<PERSON>, UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.base import Base
from app.node.node_base.node_models import WorkflowExecutionStatus, WorkflowStatus

if TYPE_CHECKING:
    from app.models.tag import Tag
    from app.models.user import User

# Association table for many-to-many relationship between workflows and tags
workflow_tag_association = Table(
    "workflow_tags",
    Base.metadata,
    Column("workflow_id", UUID(as_uuid=True), Foreign<PERSON>ey("work_flow.id"), primary_key=True),
    <PERSON>umn("tag_id", Integer, ForeignKey("tag.id"), primary_key=True),
)


class WorkFlow(Base):
    """
    WorkFlow model for storing workflow definitions.
    
    This model represents the main workflow entity with metadata and relationships
    to versions and tags. Each workflow can have multiple versions but only one
    active version at a time.
    """
    
    __tablename__ = "work_flow"
    
    # Override the id field from Base to use UUID instead of int
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid.uuid4, 
        index=True
    )
    
    # Remove the inherited id field since we're using uid as primary key
    # id = None
    
    # Basic workflow information
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, index=True)
    description: Mapped[Optional[str]] = mapped_column(String, nullable=True)

    # Foreign key to the published version
    published_version_id: Mapped[Optional[int]] = mapped_column(
        Integer,
        ForeignKey("work_flow_version.id"),
        CheckConstraint("EXISTS (SELECT 1 FROM work_flow_version WHERE id = published_version_id AND status = 'PUBLISHED')"),
        nullable=True,
        index=True
    )

    current_version_id: Mapped[Optional[int]] = mapped_column(
        Integer,
        ForeignKey("work_flow_version.id"),
        nullable=True,
        index=True,
    )

    # Audit trail fields
    created_by: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )

    edited_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        ForeignKey("users.id"),
        nullable=True,
        index=True
    )
    
    # Relationships
    versions: Mapped[List["WorkFlowVersion"]] = relationship(
        "WorkFlowVersion",
        back_populates="workflow",
        foreign_keys="WorkFlowVersion.workflow_id",
        cascade="all, delete-orphan"
    )
    
    published_version: Mapped[Optional["WorkFlowVersion"]] = relationship(
        "WorkFlowVersion",
        foreign_keys=[published_version_id],
        post_update=True
    )

    current_version: Mapped[Optional["WorkFlowVersion"]] = relationship(
        "WorkFlowVersion",
        foreign_keys=[current_version_id],
        post_update=True
    )
    
    tags: Mapped[List["Tag"]] = relationship(
        "Tag",
        secondary=workflow_tag_association,
        back_populates="workflows",
        lazy="selectin" # Tags always loaded automatically
    )

    entry_points: Mapped[List["WorkFlowEntryPoint"]] = relationship(
        "WorkFlowEntryPoint",
        back_populates="workflow",
        foreign_keys="WorkFlowEntryPoint.workflow_id",
        cascade="all, delete-orphan"
    )

    executions: Mapped[List["WorkflowExecution"]] = relationship(
        "WorkflowExecution",
        back_populates="workflow",
        foreign_keys="WorkflowExecution.workflow_id",
        cascade="all, delete-orphan"
    )

    # Audit trail relationships
    creator: Mapped["User"] = relationship(
        "User",
        foreign_keys=[created_by]
    )

    editor: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[edited_by]
    )
    
    def __repr__(self) -> str:
        return f"<WorkFlow(uid={self.id}, name={self.name}, is_active={self.is_active})>"

class WorkFlowVersion(Base):
    """
    WorkFlowVersion model for storing different versions of workflows.
    
    This model stores the actual workflow definition data and version information.
    Each version belongs to a workflow and can be tagged for easy identification.
    """
    
    __tablename__ = "work_flow_version"
    
    # Version information
    version_no: Mapped[int] = mapped_column(Integer, nullable=False, index=True)
    
    # JSONB field for storing the complete workflow definition
    work_flow: Mapped[dict] = mapped_column(JSONB, nullable=False)
    
    # Foreign keys
    workflow_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("work_flow.id"),
        nullable=False,
        index=True
    )

    # Status of the workflow version
    status: Mapped[WorkflowStatus] = mapped_column(
        Enum(WorkflowStatus),
        nullable=False,
        default=WorkflowStatus.DRAFT,
        index=True
    )
    
    version_name: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        index=True
    )

    # Audit trail fields
    created_by: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )

    edited_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        ForeignKey("users.id"),
        nullable=True,
        index=True
    )
    
    # Relationships
    workflow: Mapped["WorkFlow"] = relationship(
        "WorkFlow",
        back_populates="versions",
        foreign_keys=[workflow_id]
    )

    executions: Mapped[List["WorkflowExecution"]] = relationship(
        "WorkflowExecution",
        back_populates="workflow_version",
        foreign_keys="WorkflowExecution.workflow_version_id",
        cascade="all, delete-orphan"
    )

    # Audit trail relationships
    creator: Mapped["User"] = relationship(
        "User",
        foreign_keys=[created_by]
    )

    editor: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[edited_by]
    )
    
    # Constraints
    __table_args__ = (
        # Ensure unique version numbers per workflow
        UniqueConstraint("workflow_id", "version_no", name="uq_workflow_version"),
    )
    
    def __repr__(self) -> str:
        return f"<WorkFlowVersion(id={self.id}, workflow_id={self.workflow_id}, version_no={self.version_no})>"

class TriggerType(str, enum.Enum):
    """
    Enum for different types of workflow triggers.
    """
    WEBHOOK = "WEBHOOK"
    EVENT = "EVENT"

class WorkFlowEntryPoint(Base):

    __tablename__ = "work_flow_entry_point"

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    
    workflow_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("work_flow.id"),
        nullable=False,
        index=True
    )
    trigger_type: Mapped[enum.Enum] = mapped_column(
        Enum(TriggerType),
        nullable=False
    )
    trigger_value: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        index=True
    )
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True
    )
    start_nodes: Mapped[Optional[List[str]]] = mapped_column(
        JSONB,
        nullable=True
    )
    attributes: Mapped[Optional[dict]] = mapped_column(
        JSONB,
        nullable=True
    )
    workflow: Mapped["WorkFlow"] = relationship(
        "WorkFlow",
        back_populates="entry_points",
        foreign_keys=[workflow_id]
    )

class WorkflowExecution(Base):
    """
    WorkflowExecution model for tracking workflow runs.
    
    This model stores the execution details of a workflow run, including the
    workflow version, status, and any errors encountered during execution.
    """
    
    __tablename__ = "workflow_execution"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True)

    workflow_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("work_flow.id"),
        nullable=False,
        index=True
    )
    workflow_version_id: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("work_flow_version.id"),
        nullable=False,
        index=True
    )
    status: Mapped[WorkflowExecutionStatus] = mapped_column(
        Enum(WorkflowExecutionStatus, name="executionstatus"),
        nullable=False,
        default=WorkflowExecutionStatus.PENDING,
        index=True
    )
    error_message: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True
    )

    duration_seconds: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        index=True
    )

    billable_duration_seconds: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        index=True
    )

    result: Mapped[Optional[dict]] = mapped_column(
        JSONB,
        nullable=True
    )

    is_test: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False
    )

    start_time: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        index=True
    )

    end_time: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        index=True
    )

    created_by: Mapped[int] = mapped_column(
        Integer,
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )

    updated_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        ForeignKey("users.id"),
        nullable=True,
        index=True
    )

    # Relationships
    workflow: Mapped["WorkFlow"] = relationship(
        "WorkFlow",
        back_populates="executions",
        foreign_keys=[workflow_id]
    )

    workflow_version: Mapped["WorkFlowVersion"] = relationship(
        "WorkFlowVersion",
        back_populates="executions",
        foreign_keys=[workflow_version_id]
    )

    creator: Mapped["User"] = relationship(
        "User",
        foreign_keys=[created_by]
    )

    updater: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[updated_by]
    )