[{"name": "first_name", "display_name": "First Name", "description": "<PERSON>'s first name", "field_type": "string", "validation_rules": {"required": true, "unique": false, "min_length": 1, "max_length": 100, "min_value": null, "max_value": null, "pattern": null, "enum_values": null}, "default_value": null, "is_default_field": true, "order": 1}, {"name": "last_name", "display_name": "Last Name", "description": "<PERSON>'s last name", "field_type": "string", "validation_rules": {"required": true, "unique": false, "min_length": 1, "max_length": 100, "min_value": null, "max_value": null, "pattern": null, "enum_values": null}, "default_value": null, "is_default_field": true, "order": 2}, {"name": "email", "display_name": "Email", "description": "Contact's email address", "field_type": "email", "validation_rules": {"required": true, "unique": true, "min_length": null, "max_length": null, "min_value": null, "max_value": null, "pattern": null, "enum_values": null}, "default_value": null, "is_default_field": true, "order": 3}, {"name": "phone", "display_name": "Phone", "description": "Contact's phone number", "field_type": "phone", "validation_rules": {"required": true, "unique": false, "min_length": null, "max_length": null, "min_value": null, "max_value": null, "pattern": null, "enum_values": null}, "default_value": null, "is_default_field": true, "order": 4}]