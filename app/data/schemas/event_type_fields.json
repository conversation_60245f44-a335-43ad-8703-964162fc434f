[{"name": "name", "display_name": "Name", "description": "Name of the event type", "field_type": "string", "validation_rules": {"required": true, "unique": true, "min_length": 1, "max_length": 100, "min_value": null, "max_value": null, "pattern": "^[a-zA-Z0-9_-]+$", "enum_values": null}, "default_value": null, "is_default_field": true, "order": 0}, {"name": "description", "display_name": "Description", "description": "Description of the event type", "field_type": "string", "validation_rules": {"required": true, "unique": false, "min_length": 1, "max_length": 500, "min_value": null, "max_value": null, "pattern": null, "enum_values": null}, "default_value": null, "is_default_field": true, "order": 1}, {"name": "json_schema", "display_name": "JSON Schema", "description": "JSON schema definition for the event type", "field_type": "object", "validation_rules": {"required": true, "unique": false, "min_length": null, "max_length": null, "min_value": null, "max_value": null, "pattern": null, "enum_values": null}, "default_value": "{}", "is_default_field": true, "order": 2}]