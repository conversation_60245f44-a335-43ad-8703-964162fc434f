from  datetime import datetime, timezone
from typing import Optional
import uuid

from sqlalchemy import select
from app.models.workflow import WorkflowExecution
from sqlalchemy.ext.asyncio import AsyncSession
from app.repositories.base_repository import BaseRepository
from app.schemas.workflow import WorkflowExecution<PERSON>reate, WorkflowExecutionUpdate


class WorkflowExecutionRepository(BaseRepository[WorkflowExecution, WorkflowExecutionCreate, WorkflowExecutionUpdate]):
    """Repository for managing workflow executions."""

    def __init__(self, session: AsyncSession):
        super().__init__(WorkflowExecution, session)

    async def get_by_uid(self, uid: uuid.UUID) -> Optional[WorkflowExecution]:
        """
        Get workflow by UID.
        
        Args:
            uid: Workflow UID
            
        Returns:
            Optional[WorkflowExecution]: Workflow instance or None
        """
        query = select(WorkflowExecution).where(WorkflowExecution.id == uid)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
