"""
Repository for WorkFlow model database operations.
"""

import uuid
from typing import List, Optional, Dict, Any
from sqlalchemy import select, and_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.user import User
from app.models.workflow import WorkFlow, WorkFlowVersion
from app.models.tag import Tag
from app.repositories.base_repository import BaseRepository
from app.schemas.workflow import WorkFlowCreate, WorkFlowUpdate


class WorkFlowRepository(BaseRepository[WorkFlow, WorkFlowCreate, WorkFlowUpdate]):
    """Repository for WorkFlow model database operations."""
    
    def __init__(self, session: AsyncSession):
        super().__init__(WorkFlow, session)
    
    async def get_by_uid(self, uid: uuid.UUID) -> Optional[WorkFlow]:
        """
        Get workflow by UID.
        
        Args:
            uid: Workflow UID
            
        Returns:
            Optional[WorkFlow]: Workflow instance or None
        """
        query = select(WorkFlow).where(WorkFlow.id == uid)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_with_version(self, uid: uuid.UUID) -> Optional[WorkFlow]:
        """
        Get workflow with its active version and tags.
        
        Args:
            uid: Workflow UID
            
        Returns:
            Optional[WorkFlow]: Workflow with versions or None
        """
        query = (
            select(WorkFlow)
            .options(
            selectinload(WorkFlow.published_version).load_only(
                WorkFlowVersion.version_no,
                WorkFlowVersion.id),
            selectinload(WorkFlow.current_version),
            selectinload(WorkFlow.tags),
            selectinload(WorkFlow.creator).load_only(User.full_name),
            selectinload(WorkFlow.editor).load_only(User.full_name)
            )
            .where(WorkFlow.id == uid)
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_with_tags(self, uid: uuid.UUID) -> Optional[WorkFlow]:
        """
        Get workflow with its tags.
        
        Args:
            uid: Workflow UID
            
        Returns:
            Optional[WorkFlow]: Workflow with tags or None
        """
        query = (
            select(WorkFlow)
            .options(selectinload(WorkFlow.tags))
            .where(WorkFlow.id == uid)
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_by_name(self, name: str) -> Optional[WorkFlow]:
        """
        Get workflow by name.
        
        Args:
            name: Workflow name
            
        Returns:
            Optional[WorkFlow]: Workflow instance or None
        """
        query = select(WorkFlow).where(WorkFlow.name == name)
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_workflows(
        self,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[WorkFlow]:
        """
        Get workflows with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Additional filters
            
        Returns:
            List[WorkFlow]: List of workflows
        """
        query = select(self.model)
    
        # Join with related tables
        query = query.options(
            selectinload(self.model.tags),
            selectinload(self.model.current_version),
            selectinload(self.model.creator),
            selectinload(self.model.editor)
        )
        
        # Apply filters
        if filters:
            conditions = []
            for field, value in filters.items():
                if hasattr(self.model, field):
                    if isinstance(value, list):
                        conditions.append(getattr(self.model, field).in_(value))
                    else:
                        conditions.append(getattr(self.model, field) == value)
            if conditions:
                query = query.where(and_(*conditions))
        
        # Apply pagination
        query = query.offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return list(result.scalars().all())
    
    async def count_workflows(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count workflows.
        
        Args:
            filters: Additional filters
            
        Returns:
            int: Number of workflows
        """
        query = select(func.count(WorkFlow.id))
        
        if filters:
            if "created_by" in filters:
                query = query.where(WorkFlow.created_by == filters["created_by"])
            if "tag_ids" in filters and filters["tag_ids"]:
                query = query.join(WorkFlow.tags).where(Tag.id.in_(filters["tag_ids"]))
            if "is_active" in filters:
                query = query.where(WorkFlow.is_active == filters["is_active"])
        
        result = await self.db.execute(query)
        return result.scalar() or 0
    
    async def add_tags(self, workflow: WorkFlow, tag_ids: List[int]) -> WorkFlow:
        """
        Add tags to workflow.
        
        Args:
            workflow: Workflow instance
            tag_ids: List of tag IDs to add
            
        Returns:
            WorkFlow: Updated workflow
        """
        if tag_ids:
            # Get tags
            tag_query = select(Tag).where(Tag.id.in_(tag_ids))
            tag_result = await self.db.execute(tag_query)
            tags = list(tag_result.scalars().all())
            
            # Add tags to workflow
            for tag in tags:
                if tag not in workflow.tags:
                    workflow.tags.append(tag)
                    tag.increment_usage()
                    
        await self.db.commit()
        await self.db.refresh(workflow)
        return workflow
    
    async def remove_tags(self, workflow: WorkFlow, tag_ids: List[int]) -> WorkFlow:
        """
        Remove tags from workflow.
        
        Args:
            workflow: Workflow instance
            tag_ids: List of tag IDs to remove
            
        Returns:
            WorkFlow: Updated workflow
        """
        if tag_ids:
            # Remove tags from workflow
            tags_to_remove = [tag for tag in workflow.tags if tag.id in tag_ids]
            for tag in tags_to_remove:
                workflow.tags.remove(tag)
                tag.decrement_usage()
        
        await self.db.commit()
        await self.db.refresh(workflow)
        return workflow


class WorkFlowVersionRepository(BaseRepository[WorkFlowVersion, Any, Any]):
    """Repository for WorkFlowVersion model database operations."""
    
    def __init__(self, session: AsyncSession):
        super().__init__(WorkFlowVersion, session)
    
    async def get_by_workflow_and_version(
        self, 
        workflow_id: uuid.UUID, 
        version_no: int
    ) -> Optional[WorkFlowVersion]:
        """
        Get workflow version by workflow ID and version number.
        
        Args:
            workflow_id: Workflow UID
            version_no: Version number
            
        Returns:
            Optional[WorkFlowVersion]: Version instance or None
        """
        query = select(WorkFlowVersion).where(
            and_(
                WorkFlowVersion.workflow_id == workflow_id,
                WorkFlowVersion.version_no == version_no
            )
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def get_latest_version_number(self, workflow_id: uuid.UUID) -> int:
        """
        Get the latest version number for a workflow.
        
        Args:
            workflow_id: Workflow UID
            
        Returns:
            int: Latest version number (0 if no versions exist)
        """
        query = (
            select(func.max(WorkFlowVersion.version_no))
            .where(WorkFlowVersion.workflow_id == workflow_id)
        )
        result = await self.db.execute(query)
        max_version = result.scalar()
        return max_version or 0
    
