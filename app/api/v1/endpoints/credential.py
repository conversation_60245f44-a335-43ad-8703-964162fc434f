from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, HTTPException, Query

from app.credential.utils.credential_manager import CredentialManager
from app.credential.utils.credential_registry import CredentialRegistry
from app.credential.base.credential_model import CredentialRequestModel
from app.node.node_base.node_models import NodeFunctionRequest, NodeRequest
from app.repositories.credential_repository import CredentialRepository
from app.api.deps import get_db


router = APIRouter()

@router.get("/", response_model=list[dict])
async def get_credentials():
    return CredentialRegistry.get_all_credentials()

@router.post("/", response_model=Dict[str, Any])
async def create_credential(
    credential_request: CredentialRequestModel,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Create a new credential.
    
    This endpoint validates the credential data using the credential manager,
    encrypts sensitive information, and stores it in the database.
    
    Args:
        credential_request: The credential request model with all required parameters
        db: Database session
        
    Returns:
        Dictionary with credential information and status
    """
    try:
        # Initialize repository
        credential_repo = CredentialRepository(db)
        
        # Add credential to database
        result = await credential_repo.add_credential(
            credential_request=credential_request,
            created_by="system"  # This should come from the authenticated user
        )
        
        return {
            "status": "success",
            "message": "Credential created successfully",
            "credential": result
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create credential: {str(e)}")

@router.get("/type/{credential_type}", response_model=List[Dict[str, Any]])
async def get_credentials_by_type(
    credential_type: str,
    db: AsyncSession = Depends(get_db)
) -> List[Dict[str, Any]]:
    """
    Get all credentials of a specific type.
    
    Args:
        credential_type: The type of credential to retrieve (e.g., "whatsapp_api")
        db: Database session
        
    Returns:
        List of credential details
    """
    try:
        # Initialize repository
        credential_repo = CredentialRepository(db)
        
        # Get credentials from database
        credentials = await credential_repo.get_by_type(credential_type)
        
        # Return sanitized credential data
        return [credential.to_dict(sanitize_sensitive=True) for credential in credentials]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve credentials: {str(e)}")

@router.get("/test/{credential_id}", response_model=Dict[str, Any])
async def test_credential_by_id(
    credential_id: str,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Test an existing credential using its ID.
    
    This endpoint fetches the credential from the database, 
    decrypts its data, reconstructs the credential request model,
    and tests it by making a request to the target API.
    
    Args:
        credential_id: The UUID of the credential to test
        db: Database session
        
    Returns:
        Dictionary with test result status
    """
    try:
        import uuid
        from app.credential.utils.credential_manager import CredentialManager
        
        # Parse the string to UUID
        try:
            credential_uuid = uuid.UUID(credential_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid credential ID format")

        # Initialize credential manager
        credential_manager = CredentialManager(db = db)

        result = await credential_manager.test_credential(credential_uuid)

        return {
            "status": "success" if result else "failed"
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException as e:
        raise e
    except Exception as e:
        # Log the detailed error
        # import traceback
        # traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to test credential: {str(e)}")
    
@router.post("/authorize", response_model=Dict[str, Any])
async def generate_oauth2_authurl(
    credential_request: CredentialRequestModel,
    db: AsyncSession = Depends(get_db)  # Add this parameter to receive the request data
) -> Dict[str, Any]:
    """
    Generate OAuth2 authorization URL and optionally redirect to it.

    Args:
        node_request: NodeRequest containing OAuth2 parameters
    1. Uses CredentialManager to call the corresponding credential function
    2. Calls the specified function to generate the authorization URL
    3. Returns the authorization URL or redirects to it 

    Returns:
        Dict containing the authorization URL
    """
    try:
        import uuid
        
        try:
            credential_manager = CredentialManager(db=db)
            return await credential_manager.save_credential(credential_request, created_by="system")
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid credential ID format")
            
    except ValueError as exc:
        raise HTTPException(status_code=400, detail=str(exc))
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/callback", response_model=Any)
async def get_oauth2_callback(
    code: str = Query(None, description="Authorization code from OAuth2 provider"),
    state: str = Query(None, description="State parameter containing encoded credentials"),
    error: str = Query(None, description="Error parameter if authorization failed"),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Handle OAuth2 callback and exchange authorization code for tokens.
    """
    # return {}
    try:        
        if error:
            raise HTTPException(status_code=400, detail=f"OAuth2 authorization failed: {error}")

        if not code:
            raise HTTPException(status_code=400, detail="Authorization code is required")

        if not state:
            raise HTTPException(status_code=400, detail="State parameter is required")
            
        credential_manager = CredentialManager(db)
        result = await credential_manager.get_oauth2_callback(code, state)
        return result
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"OAuth2 callback error: {str(e)}")
    
@router.get("/{credential_id}", response_model=Dict[str, Any])
async def get_credential(
    credential_id: str,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get a credential by ID.
    
    Args:
        credential_id: The UUID of the credential
        db: Database session
        
    Returns:
        Credential details
    """
    try:
        import uuid
        # Parse the string to UUID
        try:
            credential_uuid = uuid.UUID(credential_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid credential ID format")
            
        # Initialize repository
        credential_repo = CredentialRepository(db)
        
        # Get credential from database
        credential = await credential_repo.get_by_id(credential_uuid)
        if not credential:
            raise HTTPException(status_code=404, detail=f"Credential with ID {credential_id} not found")
            
        # Return sanitized credential data
        return credential.to_dict(sanitize_sensitive=True)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve credential: {str(e)}")