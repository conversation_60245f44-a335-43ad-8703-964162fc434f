"""
WorkFlow API endpoints for workflow management.
"""

import uuid
from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, status, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_active_user
from app.core.database import get_db
from app.models.user import User
from app.schemas.common import PaginationParams
from app.schemas.workflow import (
    WorkFlowFlattenedResponse,
    WorkFlowList,
    WorkFlowUpdate,
    WorkflowExecuteRequest
)
from app.services.workflow_service import WorkFlowService
from app.services.temporal_service import TemporalService
from app.utils.exceptions import (
    NotFoundError,
    ValidationError,
    ExternalServiceError
)
from app.utils.logging import get_logger

router = APIRouter()
logger = get_logger("api.workflows")

@router.get("/new", response_model=WorkFlowFlattenedResponse, status_code=status.HTTP_200_OK)
async def create_new_workflow(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowFlattenedResponse:
    """
    Create a new empty workflow with default metadata."""
    try:
        workflow_service = WorkFlowService(db)
        new_workflow = await workflow_service.create_empty_workflow(user_id=current_user.id)
        if not new_workflow:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create new workflow")
        return workflow_service.to_flattened_response(new_workflow)
    
    except Exception as e:
        logger.error(f"Unexpected error creating new workflow: {str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/{workflow_id}", response_model=WorkFlowFlattenedResponse, status_code=status.HTTP_200_OK)
async def get_workflow(
    workflow_id: uuid.UUID,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowFlattenedResponse:
    """
    Get a workflow by ID with flattened structure combining workflow and active version data.

    This endpoint retrieves a workflow with a flattened response structure that combines:
    - Workflow metadata (uid, name, is_active, created_by, etc.)
    - Active version data (id, version_no, work_flow definition)
    - Associated tags
    - Complete workflow definition with nodes and connections

    Args:
        workflow_id: Workflow UID
        current_user: Current authenticated user
        db: Database session

    Returns:
        WorkFlowFlattenedResponse: Standardized response with flattened workflow data

    Raises:
        NotFoundError: If workflow not found or has no active version
    """
    try:
        workflow_service = WorkFlowService(db)
        workflow = await workflow_service.get_workflow(workflow_id)

        return workflow_service.to_flattened_response(workflow)
               
    except NotFoundError as e:
        logger.warning(f"Workflow not found: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving workflow {workflow_id}: {str(e)}")
        raise


@router.get("/", response_model=WorkFlowList, status_code=status.HTTP_200_OK)
async def get_workflows(
    pagination: PaginationParams = Depends(),
    active_only: bool = Query(False, description="Filter to active workflows only"),
    created_by: Optional[int] = Query(None, description="Filter by creator user ID"),
    tag_ids: Optional[str] = Query(None, description="Comma-separated tag IDs to filter by"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowList:
    """
    Get paginated list of workflows with filtering options.
    
    This endpoint supports:
    - Pagination with skip/limit
    - Filtering by active status
    - Filtering by creator
    - Filtering by associated tags
    
    Args:
        pagination: Pagination parameters (skip, limit)
        active_only: Whether to return only active workflows
        created_by: Filter by creator user ID
        tag_ids: Comma-separated tag IDs to filter by
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        WorkFlowList: Paginated workflow list with metadata
    """
    try:
        # Parse tag IDs if provided
        filters = {}
        if created_by:
            filters["created_by"] = created_by
        if tag_ids:
            try:
                tag_id_list = [int(tag_id.strip()) for tag_id in tag_ids.split(",")]
                filters["tag_ids"] = tag_id_list
            except ValueError:
                raise ValidationError("Invalid tag IDs format. Use comma-separated integers.")
        
        workflow_service = WorkFlowService(db)
        workflow_list = await workflow_service.get_workflows_paginated(
            skip=pagination.skip,
            limit=pagination.limit,
            filters=filters,
            active_only=active_only
        )
        
        logger.info(
            "Workflows list retrieved via API",
            total_count=workflow_list.total,
            page=workflow_list.page,
            requested_by=current_user.id
        )
        
        return workflow_list
        
    except ValidationError as e:
        logger.warning(f"Workflow list validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving workflows: {str(e)}")
        raise


@router.patch("/{workflow_id}", response_model=WorkFlowFlattenedResponse, status_code=status.HTTP_200_OK)
async def update_workflow(
    workflow_id: uuid.UUID,
    workflow_data: WorkFlowUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowFlattenedResponse:
    """
    Update an existing workflow by ID.
    
    This endpoint updates both the parent workflow fields (name, description, is_active, status)
    and associated version fields in a single request.
    
    Args:
        workflow_id: Workflow UID to update
        workflow_data: Workflow update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        WorkFlowFlattenedResponse: Standardized response with flattened workflow data
    
    Raises:
        NotFoundError: If workflow not found
        ValidationError: If update data is invalid
    """
    try:
        # Validate that workflow exists first (early validation)
        workflow_service = WorkFlowService(db)
        workflow = await workflow_service.update_workflow(workflow_id, workflow_data, user_id=current_user.id)

        if not workflow:
            raise NotFoundError(f"Workflow with id {workflow_id} not found")

        return workflow_service.to_flattened_response(workflow)
    except NotFoundError as e:
        logger.warning(f"Workflow update failed - not found: {str(e)}")
        raise
    except ValidationError as e:
        logger.warning(f"Workflow update validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating workflow {workflow_id}: {str(e)}")
        raise


@router.get("/{workflow_id}/publish", response_model=WorkFlowFlattenedResponse, status_code=status.HTTP_200_OK)
async def publish_workflow_version(
    workflow_id: uuid.UUID,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowFlattenedResponse:
    """
    Publish a specific version of a workflow.

    This endpoint sets the specified workflow version's status to "published",
    making it the active version of the workflow.

    Args:
        workflow_id: Workflow UID
        version_id: Version UID
        current_user: Current authenticated user
        db: Database session

    Returns:
        WorkFlowFlattenedResponse: Standardized response with flattened workflow data

    Raises:
        NotFoundError: If workflow or version not found
        ValidationError: If version is invalid
    """
    try:
        workflow_service = WorkFlowService(db)
        workflow = await workflow_service.publish_workflow_version(workflow_id, user_id=current_user.id)

        if not workflow:
            raise NotFoundError(f"Workflow with id {workflow_id} not found")

        return workflow_service.to_flattened_response(workflow)
    except NotFoundError as e:
        logger.warning(f"Workflow publish failed - not found: {str(e)}")
        raise
    except ValidationError as e:
        logger.warning(f"Workflow publish validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error publishing workflow {workflow_id}: {str(e)}")
        raise

@router.post("/execute/{workflow_id}", response_model=Dict[str, Any], status_code=status.HTTP_200_OK)
async def test_workflow_event(
    workflow_id: uuid.UUID,
    data: WorkflowExecuteRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """ Execute a workflow with test data.
    This endpoint allows executing a workflow with provided test data.
    """
    try:
        workflow_service = WorkFlowService(db)
        response = await workflow_service.execute_test_workflow(workflow_id, data, user_id=current_user.id)

        return {
            "workflow_id": str(workflow_id),
            "execution_id": response,
        }
    except NotFoundError as e:
        logger.warning(f"Workflow execution failed - not found: {str(e)}")
        raise
    except ValidationError as e:
        logger.warning(f"Workflow execution validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error executing workflow {workflow_id}: {str(e)}")
        raise

@router.get("/executions/{workflow_execution_id}", response_model=Dict[str, Any], status_code=status.HTTP_200_OK)
async def get_workflow_execution_status(
    workflow_execution_id: str,
    current_user: User = Depends(get_current_active_user),
) -> Dict[str, Any]:
    """
    Get the status of a workflow execution by its ID.

    Args:
        workflow_execution_id: The ID of the workflow execution to check
        current_user: Current authenticated user

    Returns:
        Dict[str, Any]: Status information about the workflow execution

    Raises:
        NotFoundError: If workflow execution not found
        ExternalServiceError: If Temporal service is not available
    """
    try:
        temporal_service = TemporalService()
        if not temporal_service.is_connected():
            await temporal_service.connect()

        return await temporal_service.get_workflow_result(workflow_execution_id)

    except Exception as e:
        logger.error(f"Error getting workflow execution status: {str(e)}")
        raise ExternalServiceError(
            message=f"Failed to get workflow execution status: {str(e)}",
            service_name="temporal"
        )