def db_session(session_param_name: str = "db"):
    """Inject database session into method parameters"""
    def decorator(func):
        from functools import wraps
        from app.core.database import get_db
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if session_param_name not in kwargs:
                async for db in get_db():
                    kwargs[session_param_name] = db
                    return await func(*args, **kwargs)
            return await func(*args, **kwargs)
        return wrapper
    return decorator
