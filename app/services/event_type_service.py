"""
Event Type service layer for business logic and orchestration.
"""

from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from app.repositories.event_type_repository import EventTypeRepository
from app.schemas.event_type import (
    EventType,
    EventTypeBase,
    EventTypeCreate,
    EventType<PERSON><PERSON><PERSON>pdate,
    EventType<PERSON>pdate,
    EventTypeList,
    ValidationRequest,
    ValidationResponse
)
from app.services.validation_service import ValidationService
from app.utils.exceptions import NotFoundError, ValidationError, ConflictError
from app.utils.logging import get_logger

logger = get_logger("services.event_type")


class EventTypeService:
    """Service for event type management and validation."""
    
    def __init__(self, repository: EventTypeRepository):
        """
        Initialize service with repository.
        
        Args:
            repository: Event type repository instance
        """
        self.repository = repository
        self.validation_service = ValidationService()
    
    async def create_event_type(self, event_type_data: EventTypeBase, created_by: int) -> Dict[str, Any]:
        """
        Create a new event type with schema validation.
        
        Args:
            event_type_data: Event type creation data
            
        Returns:
            EventType: Created event type
            
        Raises:
            ValidationError: If schema validation fails
            ConflictError: If event type name already exists
        """
        logger.info(f"Creating event type: {event_type_data.name}")
        
        # Validate the schema definition
        try:
            self.validation_service.validate_schema_definition(event_type_data.schema)
        except ValidationError as e:
            logger.error(f"Schema validation failed for {event_type_data.name}: {e}")
            raise
        
        # Check if name already exists
        existing = await self.repository.get_by_name(event_type_data.name)
        if existing:
            raise ConflictError(f"Event type with name '{event_type_data.name}' already exists")
        
        data = event_type_data.model_dump()
        data["created_by"] = created_by
        event_type_data = EventTypeCreate(**data)

        # Create the event type
        created_doc = await self.repository.create(event_type_data)
        
        logger.info(f"Successfully created event type: {event_type_data.name}")
        return (EventType(**created_doc)).to_dict()
    
    async def get_event_type_by_id(self, event_type_id: str) -> Dict[str, Any]:
        """
        Get event type by ID.
        
        Args:
            event_type_id: Event type ID
            
        Returns:
            EventType: Event type instance
            
        Raises:
            NotFoundError: If event type not found
        """
        document = await self.repository.get_by_id(event_type_id)
        if not document:
            raise NotFoundError(f"Event type with id '{event_type_id}' not found")
        
        return (EventType(**document)).to_dict()
    
    async def get_event_type_by_name(self, name: str) -> EventType:
        """
        Get event type by name.
        
        Args:
            name: Event type name
            
        Returns:
            EventType: Event type instance
            
        Raises:
            NotFoundError: If event type not found
        """
        document = await self.repository.get_by_name(name)
        if not document:
            raise NotFoundError(f"Event type with name '{name}' not found")
        
        return EventType(**document)
    
    async def get_event_types(
        self,
        skip: int = 0,
        limit: int = 100,
        created_by: Optional[int] = None,
        name_filter: Optional[str] = None
    ) -> List[EventTypeList]:
        """
        Get multiple event types with pagination and filtering.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            created_by: Filter by creator user ID
            name_filter: Filter by name (partial match)
            
        Returns:
            List[EventTypeList]: List of event types
        """
        # Build filters
        filters = {}
        if created_by:
            filters["created_by"] = created_by
        if name_filter:
            filters["name"] = {"$regex": name_filter, "$options": "i"}
        
        documents = await self.repository.get_multi(
            skip=skip,
            limit=limit,
            filters=filters,
            sort_by="_created_on",
            sort_order=-1
        )
        
        return [EventTypeList(**doc) for doc in documents]
    
    async def count_event_types(
        self,
        created_by: Optional[int] = None,
        name_filter: Optional[str] = None
    ) -> int:
        """
        Count event types with optional filtering.
        
        Args:
            created_by: Filter by creator user ID
            name_filter: Filter by name (partial match)
            
        Returns:
            int: Number of matching event types
        """
        # Build filters
        filters = {}
        if created_by:
            filters["created_by"] = created_by
        if name_filter:
            filters["name"] = {"$regex": name_filter, "$options": "i"}
        
        return await self.repository.count(filters)
    
    async def update_event_type(
        self,
        event_type_id: str,
        update_data: EventTypeUpdate,
        edited_by: int
    ) -> dict[str, Any]:
        """
        Update an existing event type.
        
        Args:
            event_type_id: Event type ID
            update_data: Update data
            
        Returns:
            EventType: Updated event type
            
        Raises:
            NotFoundError: If event type not found
            ValidationError: If schema validation fails
            ConflictError: If name conflict occurs
        """
        logger.info(f"Updating event type: {event_type_id}")
        
        # Check if event type exists
        existing = await self.repository.get_by_id(event_type_id)
        if not existing:
            raise NotFoundError(f"Event type with id '{event_type_id}' not found")
        
        # Validate schema if provided
        if update_data.schema is not None:
            try:
                self.validation_service.validate_schema_definition(update_data.schema)
            except ValidationError as e:
                logger.error(f"Schema validation failed for update: {e}")
                raise
        
        # Check for name conflicts if name is being updated
        if update_data.name is not None and update_data.name != existing["name"]:
            existing_with_name = await self.repository.get_by_name(update_data.name)
            if existing_with_name and existing_with_name["id"] != event_type_id:
                raise ConflictError(f"Event type with name '{update_data.name}' already exists")
            
        data = update_data.model_dump()
        data["edited_by"] = edited_by
        update_data = EventTypeDBUpdate(**data)
        
        # Update the event type
        updated_doc = await self.repository.update(event_type_id, update_data)
        if not updated_doc:
            raise NotFoundError(f"Event type with id '{event_type_id}' not found")
        
        logger.info(f"Successfully updated event type: {event_type_id}")
        return (EventType(**updated_doc)).to_dict()
    
    async def delete_event_type(self, event_type_id: str) -> bool:
        """
        Delete an event type.
        
        Args:
            event_type_id: Event type ID
            
        Returns:
            bool: True if deleted successfully
            
        Raises:
            NotFoundError: If event type not found
        """
        logger.info(f"Deleting event type: {event_type_id}")
        
        # Check if event type exists
        if not await self.repository.exists(event_type_id):
            raise NotFoundError(f"Event type with id '{event_type_id}' not found")
        
        # Delete the event type
        deleted = await self.repository.delete(event_type_id)
        
        if deleted:
            logger.info(f"Successfully deleted event type: {event_type_id}")
        
        return deleted
    
    async def validate_data(
        self,
        event_type_id: str,
        validation_request: ValidationRequest
    ) -> ValidationResponse:
        """
        Validate data against an event type schema.
        
        Args:
            event_type_id: Event type ID
            validation_request: Validation request with data
            
        Returns:
            ValidationResponse: Validation result
            
        Raises:
            NotFoundError: If event type not found
        """
        logger.debug(f"Validating data against event type: {event_type_id}")
        
        # Get the event type
        event_type_doc = await self.repository.get_by_id(event_type_id)
        if not event_type_doc:
            raise NotFoundError(f"Event type with id '{event_type_id}' not found")
        
        # Validate the data
        validation_result = self.validation_service.validate_data_against_schema(
            validation_request.data,
            event_type_doc["schema"]
        )
        
        logger.debug(f"Validation result: valid={validation_result.valid}, errors={len(validation_result.errors)}")
        return validation_result
    
    async def get_schema_summary(self, event_type_id: str) -> Dict[str, Any]:
        """
        Get a summary of an event type schema.
        
        Args:
            event_type_id: Event type ID
            
        Returns:
            Dict[str, Any]: Schema summary
            
        Raises:
            NotFoundError: If event type not found
        """
        # Get the event type
        event_type_doc = await self.repository.get_by_id(event_type_id)
        if not event_type_doc:
            raise NotFoundError(f"Event type with id '{event_type_id}' not found")
        
        # Generate schema summary
        return self.validation_service.get_schema_summary(event_type_doc["schema"])
    
    async def get_detailed_validation_report(
        self,
        event_type_id: str,
        validation_request: ValidationRequest
    ) -> Dict[str, Any]:
        """
        Get a detailed validation report for data against an event type schema.
        
        Args:
            event_type_id: Event type ID
            validation_request: Validation request with data
            
        Returns:
            Dict[str, Any]: Detailed validation report
            
        Raises:
            NotFoundError: If event type not found
        """
        # Get the event type
        event_type_doc = await self.repository.get_by_id(event_type_id)
        if not event_type_doc:
            raise NotFoundError(f"Event type with id '{event_type_id}' not found")
        
        # Generate detailed report
        return self.validation_service.validate_and_get_detailed_report(
            validation_request.data,
            event_type_doc["schema"]
        )
