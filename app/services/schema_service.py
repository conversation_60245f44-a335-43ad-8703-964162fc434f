"""
Business logic service for dynamic schema management.
"""

from typing import Dict, List, Optional, Any
from uuid import UUID
import asyncio
import json
from pathlib import Path

from motor.motor_asyncio import AsyncIOMotorDatabase

from app.repositories.schema_repository import SchemaRepository
from app.schemas.schema import (
    SchemaDefinitionCreate, SchemaDefinitionUpdate, SchemaDefinition, SchemaDefinitionList, SchemaField,
    FieldType, ValidationRule, SchemaValidationResponse, SchemaValidationError
)
from app.utils.exceptions import NotFoundError, ValidationError, ConflictError
from app.utils.logging import get_logger

logger = get_logger("services.schema")

from app.utils.constant import AUDIT_FIELD_NAMES

class SchemaService:
    """Service for schema management with caching and validation."""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        """
        Initialize service with database.
        
        Args:
            db: MongoDB database instance
        """
        self.repository = SchemaRepository(db)
        self._schema_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_lock = asyncio.Lock()
    
    async def initialize(self) -> None:
        """Initialize the service and create default schemas."""
        await self.repository.ensure_indexes()
        await self._create_default_schemas()
        
    
    async def create_schema(self, schema_data: SchemaDefinitionCreate) -> SchemaDefinition:
        """
        Create a new schema.

        Args:
            schema_data: Schema creation data

        Returns:
            Schema: Created schema

        Raises:
            ValidationError: If schema validation fails
            ConflictError: If schema name already exists
        """
        # Import here to avoid circular imports
        from app.services.schema_validation_service import SchemaValidationService

        # Validate schema definition
        validation_errors = SchemaValidationService.validate_schema_definition(schema_data.fields)
        if validation_errors:
            raise ValidationError(f"Schema validation failed: {'; '.join(validation_errors)}")

        # Check if schema name already exists
        existing = await self.repository.get_by_name(schema_data.name)
        if existing:
            raise ConflictError(f"Schema with name '{schema_data.name}' already exists")

        # Create schema
        created_doc = await self.repository.create(schema_data)

        # Create collection for schema
        await self._create_collection_for_schema(schema_data)

        # Update cache
        async with self._cache_lock:
            self._schema_cache[schema_data.name] = created_doc

        # Convert to response model
        return self._doc_to_schema(created_doc)
    
    async def _create_collection_for_schema(self, schema_data: SchemaDefinitionCreate) -> None:
        """
        Create a MongoDB collection for the schema with validation rules.
        
        Args:
            schema_data: Schema creation data
        """
        collection_name = schema_data.name
        db = self.repository.db
        
        # Check if collection already exists
        existing_collections = await db.list_collection_names()
        if collection_name in existing_collections:
            logger.warning(f"Collection '{collection_name}' already exists")
            return
        
        # Create collection
        await db.create_collection(collection_name)
        logger.info(f"Created collection '{collection_name}'")

        # Build JSON Schema validation from fields
        json_schema = self._build_json_schema_from_fields(schema_data.fields)
        
        # Apply validation schema to collection
        validator = {
            "$jsonSchema": json_schema
        }
        
        await db.command({
            "collMod": collection_name,
            "validator": validator,
            "validationLevel": "moderate"  # allows existing documents to remain valid
        })
        
        logger.info(f"Applied schema validation to collection '{collection_name}'")

    def _build_json_schema_from_fields(self, fields: List[SchemaField]) -> Dict[str, Any]:
        """
        Build MongoDB JSON Schema from schema fields.
        
        Args:
            fields: Schema field definitions
            
        Returns:
            Dict: MongoDB JSON Schema
        """
        properties: Dict[str, Any] = {}
        required = []
        
        for field in fields:
            # Add to required list if field is required
            if field.validation_rules.required:
                required.append(field.name)
            
            # Build property definition based on field type
            property_def: Dict[str, Any] = {
                "description": field.description or field.display_name
            }
            
            # Map field types to MongoDB types
            if field.field_type == FieldType.STRING or field.field_type == FieldType.EMAIL or \
               field.field_type == FieldType.PHONE or field.field_type == FieldType.URL:
                property_def["bsonType"] = "string"
                
                # Add string validations
                if field.validation_rules.min_length is not None:
                    property_def["minLength"] = int(field.validation_rules.min_length)
                if field.validation_rules.max_length is not None:
                    property_def["maxLength"] = int(field.validation_rules.max_length)
                if field.validation_rules.pattern:
                    property_def["pattern"] = field.validation_rules.pattern
                if field.validation_rules.enum_values:
                    property_def["enum"] = [str(v) for v in field.validation_rules.enum_values]
                    
            elif field.field_type == FieldType.NUMBER:
                property_def["bsonType"] = "double"
                if field.validation_rules.min_value is not None:
                    property_def["minimum"] = float(field.validation_rules.min_value)
                if field.validation_rules.max_value is not None:
                    property_def["maximum"] = float(field.validation_rules.max_value)
                
            elif field.field_type == FieldType.INTEGER:
                property_def["bsonType"] = "int"
                if field.validation_rules.min_value is not None:
                    property_def["minimum"] = int(field.validation_rules.min_value)
                if field.validation_rules.max_value is not None:
                    property_def["maximum"] = int(field.validation_rules.max_value)
                
            elif field.field_type == FieldType.BOOLEAN:
                property_def["bsonType"] = "bool"
            
            elif field.field_type == FieldType.DATE or field.field_type == FieldType.DATETIME:
                # For date/datetime fields, use "date" bsonType
                property_def["bsonType"] = "date"
                
            elif field.field_type == FieldType.OBJECT:
                property_def["bsonType"] = "object"
            
            elif field.field_type == FieldType.OBJECTID:
                property_def["bsonType"] = "objectId"
                
            elif field.field_type == FieldType.ARRAY:
                property_def["bsonType"] = "array"
        
            properties[field.name] = property_def
        
        return {
            "bsonType": "object",
            "required": required,
            "properties": properties,
            "additionalProperties": False  # Restrict to only defined properties
        }
    
    async def get_schema_by_id(self, schema_id: str) -> SchemaDefinition:
        """
        Get schema Definition by ID.
        
        Args:
            schema_id: Schema ID
            
        Returns:
            Schema: Schema instance
            
        Raises:
            NotFoundError: If schema not found
        """
        doc = await self.repository.get_by_id(schema_id)
        if not doc:
            raise NotFoundError(f"Schema with ID '{schema_id}' not found")
        
        return self._doc_to_schema(doc)
    
    async def get_schema_by_name(self, name: str, use_cache: bool = True) -> SchemaDefinition:
        """
        Get schema by name.
        
        Args:
            name: Schema name
            use_cache: Whether to use cache
            
        Returns:
            Schema: Schema instance
            
        Raises:
            NotFoundError: If schema not found
        """
        # Check cache first
        if use_cache:
            async with self._cache_lock:
                if name in self._schema_cache:
                    return self._doc_to_schema(self._schema_cache[name])
        
        # Get from database
        doc = await self.repository.get_by_name(name)
        if not doc:
            raise NotFoundError(f"Schema with name '{name}' not found")
        
        # Update cache
        if use_cache:
            async with self._cache_lock:
                self._schema_cache[name] = doc
        
        return self._doc_to_schema(doc)
    
    async def get_schemas(
        self,
        skip: int = 0,
        limit: int = 100,
        created_by: Optional[int] = None,
        include_system: bool = True
    ) -> SchemaDefinitionList:
        """
        Get multiple schemas with pagination.
        
        Args:
            skip: Number of schemas to skip
            limit: Maximum number of schemas to return
            created_by: Filter by creator
            include_system: Whether to include system schemas
            
        Returns:
            SchemaList: List of schemas with pagination info
        """
        filters = {}
        if created_by:
            filters["created_by"] = created_by
        if not include_system:
            filters["is_system_schema"] = False
        
        # Get schemas and count
        docs = await self.repository.get_multi(skip=skip, limit=limit, filters=filters)
        total = await self.repository.count(filters=filters)
        
        # Convert to response models
        schemas = [self._doc_to_schema(doc) for doc in docs]
        
        return SchemaDefinitionList(
            schemas=schemas,
            total=total,
            page=(skip // limit) + 1,
            page_size=limit
        )
    
    async def update_schema(self, schema_id: str, update_data: SchemaDefinitionUpdate) -> SchemaDefinition:
        """
        Update an existing schema Definition.
        
        Args:
            schema_id: Schema ID
            update_data: Update data
            
        Returns:
            Schema: Updated schema
            
        Raises:
            NotFoundError: If schema not found
            ValidationError: If update validation fails
        """

        # Import here to avoid circular imports
        from app.services.schema_validation_service import SchemaValidationService

        # Get existing schema
        existing_doc = await self.repository.get_by_id(schema_id)

        if not existing_doc:
            raise NotFoundError(f"Schema Definition with ID '{schema_id}' not found")
        
        # Check if it's a system schema
        if existing_doc.get("is_system_schema", False):
            # For system schemas, only allow adding new fields, not modifying existing ones
            if update_data.fields:
                await self._validate_system_schema_update(existing_doc, update_data.fields)
        
        if update_data.fields is not None:
            # Validate field definitions
            validation_errors = SchemaValidationService.validate_schema_definition(update_data.fields)
            if validation_errors:
                raise ValidationError(f"Schema validation failed: {'; '.join(validation_errors)}")
            
            # Ensure audit fields are preserved
            existing_fields = existing_doc.get("fields", [])
            existing_audit_fields = [field for field in existing_fields 
                                    if field.get("name") in AUDIT_FIELD_NAMES]
            
            # Convert to SchemaField objects if they're in dict form
            existing_audit_field_objects = []
            for field in existing_audit_fields:
                if isinstance(field, dict):
                    field_obj = SchemaField(**field)
                    existing_audit_field_objects.append(field_obj)
                else:
                    existing_audit_field_objects.append(field)
            
            # Get new field names
            new_field_names = {field.name for field in update_data.fields}
            
            # Only add audit fields that don't exist in the update
            preserved_audit_fields = [field for field in existing_audit_field_objects 
                                    if field.name not in new_field_names]
            
            # Append preserved audit fields to the update
            update_data.fields.extend(preserved_audit_fields)
        
        # Update schema
        updated_doc = await self.repository.update(schema_id, update_data)
        if not updated_doc:
            raise NotFoundError(f"Schema with ID '{schema_id}' not found or could not be updated")
        
        # Always update collection validation schema, using all fields from the updated document
        try:
            collection_name = updated_doc["name"]
            
            # Get all fields from the updated document
            all_fields = []
            for field_doc in updated_doc["fields"]:
                validation_rules = ValidationRule(**field_doc["validation_rules"])
                field = SchemaField(
                    name=field_doc["name"],
                    display_name=field_doc["display_name"],
                    description=field_doc.get("description"),
                    field_type=FieldType(field_doc["field_type"]),
                    validation_rules=validation_rules,
                    default_value=field_doc.get("default_value"),
                    is_default_field=field_doc.get("is_default_field", False),
                    order=field_doc.get("order", 0)
                )
                all_fields.append(field)
            

            # Rebuild JSON Schema from all fields
            json_schema = self._build_json_schema_from_fields(all_fields)
            # Apply updated validation to collection
            validator = {"$jsonSchema": json_schema}
            await self.repository.db.command({
                "collMod": collection_name,
                "validator": validator,
                "validationLevel": "strict"
            })

            coll_info = await self.repository.db.command("listCollections", filter={"name": collection_name})
            current_validator = coll_info['cursor']['firstBatch'][0].get('options', {}).get('validator')

            logger.info(f"Updated schema validation for collection '{collection_name}'")
        except Exception as e:
            logger.error(f"Failed to update collection validation: {e}")
            # Consider whether to raise exception here for critical errors
    
        # Update cache
        async with self._cache_lock:
            schema_name = updated_doc["name"]
            self._schema_cache[schema_name] = updated_doc
        
        return self._doc_to_schema(updated_doc)
    
    async def delete_schema(self, schema_id: str) -> bool:
        """
        Delete a schema Definition.
        
        Args:
            schema_id: Schema ID
            
        Returns:
            bool: True if deleted
            
        Raises:
            NotFoundError: If schema not found
            ValidationError: If schema cannot be deleted
        """
        # Get existing schema
        existing_doc = await self.repository.get_by_id(schema_id)
        if not existing_doc:
            raise NotFoundError(f"Schema with ID '{schema_id}' not found")
        
        # Check if it's a system schema
        if existing_doc.get("is_system_schema", False):
            raise ValidationError("System schemas cannot be deleted")
        
        # Delete schema
        deleted = await self.repository.delete(schema_id)
        
        # Remove from cache
        if deleted:
            async with self._cache_lock:
                schema_name = existing_doc["name"]
                self._schema_cache.pop(schema_name, None)
        
        return deleted
    
    async def validate_schema_definition(self, fields: List[SchemaField]) -> SchemaValidationResponse:
        """
        Validate schema field definitions.
        
        Args:
            fields: Schema fields to validate
            
        Returns:
            SchemaValidationResponse: Validation result
        """
        # Import here to avoid circular imports
        from app.services.schema_validation_service import SchemaValidationService

        validation_errors = SchemaValidationService.validate_schema_definition(fields)
        
        errors = [
            SchemaValidationError(field="schema", message=error, invalid_value=None)
            for error in validation_errors
        ]
        
        return SchemaValidationResponse(
            valid=len(errors) == 0,
            errors=errors
        )
    
    async def get_schema_fields(self, schema_name: str) -> List[SchemaField]:
        """
        Get schema fields by name.
        
        Args:
            schema_name: Schema name
            
        Returns:
            List[SchemaField]: Schema fields
            
        Raises:
            NotFoundError: If schema not found
        """
        schema = await self.get_schema_by_name(schema_name)
        return schema.fields
    
    async def clear_cache(self) -> None:
        """Clear the schema cache."""
        async with self._cache_lock:
            self._schema_cache.clear()
    
    async def _create_default_schemas(self) -> None:
        """Create all default schemas if they don't exist."""
        try:
            # Define default schemas configuration
            default_schemas = [
                {
                    "name": "contact",
                    "display_name": "Contact",
                    "description": "Default contact schema with mandatory fields",
                    "file_name": "contact_fields.json"
                },
                {
                    "name": "event",
                    "display_name": "Event", 
                    "description": "Default event schema with mandatory fields",
                    "file_name": "event_fields.json"
                },
                {
                    "name": "event_type",
                    "display_name": "Event Type",
                    "description": "Default event type schema with mandatory fields",
                    "file_name": "event_type_fields.json"
                }
            ]

            data_dir = Path(__file__).parent.parent / "data" / "schemas"

            for schema_config in default_schemas:
                try:
                    # Check if schema already exists
                    existing = await self.repository.get_by_name(schema_config["name"])
                    if existing:
                        logger.info(f"{schema_config['display_name']} schema already exists")
                        continue

                    # Load fields from JSON file
                    fields_path = data_dir / schema_config["file_name"]
                    try:
                        with open(fields_path, "r") as f:
                            fields_data = json.load(f)
                        logger.info(f"Loaded {schema_config['name']} fields from {fields_path}")
                    except Exception as e:
                        logger.error(f"Failed to load {schema_config['name']} fields from JSON: {e}")
                        continue

                    # Create schema data
                    schema_data = SchemaDefinitionCreate(
                        name=schema_config["name"],
                        display_name=schema_config["display_name"],
                        description=schema_config["description"],
                        fields=[SchemaField(**field) for field in fields_data],
                        created_by=0
                    )
                    # Create the schema
                    await self.create_schema_definition(schema_data)
                    logger.info(f"Created default {schema_config['display_name']} schema")

                except Exception as e:
                    logger.error(f"Failed to create default {schema_config['display_name']} schema: {e}")
                    # Continue with next schema instead of stopping completely

        except Exception as e:
            logger.error(f"Failed to create default schemas: {e}")

    async def _validate_system_schema_update(self, existing_doc: Dict[str, Any], new_fields: List[SchemaField]) -> None:
        """
        Validate updates to system schemas.
        
        Args:
            existing_doc: Existing schema document
            new_fields: New field definitions
            
        Raises:
            ValidationError: If update is not allowed
        """
        existing_fields = {field["name"]: field for field in existing_doc["fields"]}
        
        for field in new_fields:
            if field.name in existing_fields:
                existing_field = existing_fields[field.name]
                
                # Check if it's a default field
                if existing_field.get("is_default_field", False):
                    # Only allow changes to non-critical properties
                    if (field.field_type.value != existing_field["field_type"] or
                        field.validation_rules.required != existing_field["validation_rules"]["required"]):
                        raise ValidationError(
                            f"Cannot modify type or required status of default field '{field.name}'"
                        )
    
    def _doc_to_schema(self, doc: Dict[str, Any]) -> SchemaDefinition:
        """
        Convert database document to Schema model.
        
        Args:
            doc: Database document
            
        Returns:
            Schema: Schema model
        """
        # Convert fields
        fields = []
        for field_doc in doc["fields"]:
            validation_rules = ValidationRule(**field_doc["validation_rules"])
            field = SchemaField(
                name=field_doc["name"],
                display_name=field_doc["display_name"],
                description=field_doc.get("description"),
                field_type=FieldType(field_doc["field_type"]),
                validation_rules=validation_rules,
                default_value=field_doc.get("default_value"),
                is_default_field=field_doc.get("is_default_field", False),
                order=field_doc.get("order", 0)
            )
            fields.append(field)
        
        from datetime import datetime
        created_on = self._ensure_datetime(doc.get("_created_on")) or datetime.now()
        updated_on = self._ensure_datetime(doc.get("_updated_on")) or datetime.now()
        return SchemaDefinition(
            id=doc["id"],
            name=doc["name"],
            display_name=doc["display_name"],
            description=doc.get("description"),
            fields=fields,
            version=doc["version"],
            created_by=doc.get("created_by"),
            updated_by=doc.get("updated_by"),
            is_system_schema=doc.get("is_system_schema", False),
            _created_on=created_on,
            _updated_on=updated_on
        )

    def _ensure_datetime(self, value):
        """
        Ensure the value is a datetime object, or return None.
        """
        from datetime import datetime
        if value is None:
            return None
        if isinstance(value, datetime):
            return value
        try:
            # Try parsing ISO format string
            return datetime.fromisoformat(value)
        except Exception:
            return None

    async def create_schema_definition(self, schema_data: SchemaDefinitionCreate) -> SchemaDefinition:
        """Create a new schema definition with default system fields."""
        # Check for conflicts between user fields and system fields
        user_field_names = {field.name for field in schema_data.fields}
        system_fields = self.get_default_system_fields()
        system_field_names = {field.name for field in system_fields}
        conflicts = user_field_names.intersection(system_field_names)
        if conflicts:
            raise ValidationError(f"Field names conflict with system fields: {', '.join(conflicts)}")
        
        # Add system fields to user fields
        schema_data.fields.extend(system_fields)
        # Create the schema with combined fields
        created_schema =await self.create_schema(schema_data)
        return created_schema

    @staticmethod
    def get_default_system_fields() -> List[SchemaField]:
        """Generate the default system fields that should be added to every schema."""
        return [
            SchemaField(
                name="_id",
                display_name="ID",
                description="Unique identifier",
                field_type=FieldType.OBJECTID,
                validation_rules=ValidationRule(
                    required=True,
                    unique=True,
                    min_length=None,
                    max_length=None,
                    min_value=None,
                    max_value=None,
                    pattern=None,
                    enum_values=None
                ),
                default_value=None,
                is_default_field=True,
                order=0
            ),
            SchemaField(
                name="_created_on",
                display_name="Created On",
                description="Timestamp when record was created",
                field_type=FieldType.DATETIME,
                validation_rules=ValidationRule(
                    required=True,
                    unique=False,
                    min_length=None,
                    max_length=None,
                    min_value=None,
                    max_value=None,
                    pattern=None,
                    enum_values=None
                ),
                default_value=None,
                is_default_field=True,
                order=1001
            ),
            SchemaField(
                name="_created_by",
                display_name="Created By", 
                description="Created by",
                field_type=FieldType.STRING,
                validation_rules=ValidationRule(
                    required=True,
                    unique=False,
                    min_length=None,
                    max_length=None,
                    min_value=None,
                    max_value=None,
                    pattern=None,
                    enum_values=None
                ),
                default_value=None,
                is_default_field=True,
                order=1002
            ),
            SchemaField(
                name="_created_by_source",
                display_name="Created by Entity type",
                description="Created By Entity type (workflow or user)",
                field_type=FieldType.STRING,
                validation_rules=ValidationRule(
                    required=True,
                    unique=False,
                    min_length=None,
                    max_length=None,
                    min_value=None,
                    max_value=None,
                    pattern=None,
                    enum_values=["workflow", "user"]
                ),
                default_value="user",
                is_default_field=False,
                order=1003
            ),
            SchemaField(
                name="_updated_on",
                display_name="Updated On", 
                description="Timestamp when record was last updated",
                field_type=FieldType.DATETIME,
                validation_rules=ValidationRule(
                    required=True,
                    unique=False,
                    min_length=None,
                    max_length=None,
                    min_value=None,
                    max_value=None,
                    pattern=None,
                    enum_values=None
                ),
                default_value=None,
                is_default_field=True,
                order=1004
            ),
            SchemaField(
                name="_updated_by",
                display_name="Update By", 
                description="Updated by",
                field_type=FieldType.STRING,
                validation_rules=ValidationRule(
                    required=True,
                    unique=False,
                    min_length=None,
                    max_length=None,
                    min_value=None,
                    max_value=None,
                    pattern=None,
                    enum_values=None
                ),
                default_value=None,
                is_default_field=True,
                order=1005
            ),
            SchemaField(
                name="_updated_by_source",
                display_name="Updated by Entity type",
                description="Updated by Entity type (workflow or user)",
                field_type=FieldType.STRING,
                validation_rules=ValidationRule(
                    required=True,
                    unique=False,
                    min_length=None,
                    max_length=None,
                    min_value=None,
                    max_value=None,
                    pattern=None,
                    enum_values=["workflow", "user"]
                ),
                default_value="user",
                is_default_field=False,
                order=1006
            )
        ]
