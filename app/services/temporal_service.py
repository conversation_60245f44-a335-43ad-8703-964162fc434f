import asyncio
from typing import Any, Optional, cast

from sqlalchemy.ext.asyncio import AsyncSession
from app.schemas.workflow import WorkflowExecutionCreate
from app.utils.db_util import db_session
from app.models.workflow import WorkflowExecution
from app.utils.logging import get_logger
from temporalio.client import Client
from temporalio.worker import Worker

from app.core.config import settings
from app.workflow_manager.event_workflow import EventWorkflow
from app.workflow_manager.state_update_activity import state_update_activity
from app.workflow_manager.test_event_workflow import TestEventWorkflow
from app.workflow_manager.workflow_activity import workflow_activity

logger = get_logger("services.temporal")

class TemporalService:
    """
    Service for interacting with Temporal workflows.
    """

    def __init__(self):
        """
        Initialize the TemporalService with a Temporal client.
        """
        self.client : Optional[Client] = None
        self.worker: Optional[Worker] = None
        self.test_worker: Optional[Worker] = None
        self._worker_task: Optional[asyncio.Task] = None
        self._test_worker_task: Optional[asyncio.Task] = None
        self._connected = False
        
        # Configuration from environment variables
        self.temporal_host = settings.TEMPORAL_HOST
        self.temporal_port = settings.TEMPORAL_PORT
        self.temporal_namespace = settings.TEMPORAL_NAMESPACE
        self.task_queue = "event-task-queue"
        self.test_task_queue = "test-event-task-queue"

    async def connect(self):
        """
        Connect to Temporal server and start workers.
        
        Returns:
            bool: True if connection was successful, False otherwise
        """
        try:
            # Connect to Temporal server
            self.client = await Client.connect(
                f"{self.temporal_host}:{self.temporal_port}",
                namespace=self.temporal_namespace,
            )
            
            logger.info(
                "Connected to Temporal server",
                host=self.temporal_host,
                port=self.temporal_port,
                namespace=self.temporal_namespace
            )

            # Create and start worker
            self.worker = Worker(
                self.client,
                task_queue=self.task_queue,
                workflows=[EventWorkflow],
                activities=[workflow_activity, state_update_activity],
                
            )
            # Create and start test worker
            self.test_worker = Worker(
                self.client,
                task_queue=self.test_task_queue,
                workflows=[TestEventWorkflow],
                activities=[workflow_activity, state_update_activity],
                
            )
            
            # Start worker in background task
            self._worker_task = asyncio.create_task(self.worker.run())
            self._test_worker_task = asyncio.create_task(self.test_worker.run())
            
            # Wait a moment to ensure worker is started
            await asyncio.sleep(1)
            
            self._connected = True
            logger.info("Temporal worker started", task_queue=self.task_queue)
            
            return True
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            logger.error("Failed to start Temporal client/worker", error=str(e))
            self._connected = False
            return False

    async def disconnect(self):
        """Stop the Temporal worker and close client connection."""
        try:
            if self._worker_task and not self._worker_task.done():
                self._worker_task.cancel()
                try:
                    await self._worker_task
                except asyncio.CancelledError:
                    pass
            
            if self.worker:
                await self.worker.shutdown()
            
            self._connected = False
            logger.info("Temporal worker stopped")
            
        except Exception as e:
            logger.error("Error stopping Temporal worker", error=str(e))

    def is_connected(self) -> bool:
        """Check if the client is connected to Temporal."""
        return self._connected and self.client is not None
   
    async def start_workflow(self, data: WorkflowExecutionCreate) -> str:
        """
        Start a workflow with the given ID and model.

        :param id: The unique identifier for the workflow.
        :param workflow: The workflow model containing details.
        :param is_test: Whether this is a test workflow.
        :return: The ID of the started workflow.
        """
        if not self.is_connected():
            await self.connect()
        
        client = cast(Client, self.client)
        result = await client.start_workflow(
            "test_event_workflow" if data.is_test else "event_workflow",
            data.result,
            id=str(data.id),
            task_queue=self.test_task_queue if data.is_test else self.task_queue,
        )
        await self.add_execution(data)

        return result.id
        

    async def get_workflow_result(self, workflow_id: str) -> Any:
        """Get the result of a completed workflow."""
        if not self.is_connected():
            raise RuntimeError("Temporal client is not connected")
        else:
            handle = cast(Client,self.client).get_workflow_handle(workflow_id)
            return await handle.query("get_event_workflow_status")

    
    @db_session()
    async def add_execution(
        self, data: WorkflowExecutionCreate,
        db: Optional[AsyncSession] = None
    ) -> None: 
        """
        Update the status of a workflow execution.

        :param workflow_id: The ID of the workflow execution.
        :param status: The new status to set.
        """
        if db is None:
            raise ValueError("Database session is required to update execution status")
        
        execution = WorkflowExecution(**data.model_dump(exclude_none=True))
        db.add(execution)
        await db.commit()
        await db.refresh(execution)
        