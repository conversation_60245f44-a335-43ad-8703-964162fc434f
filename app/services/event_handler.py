"""
Event handler for processing events from AWS Lambda via RabbitMQ.
"""
import json
from typing import Dict, Any, Optional
from uuid import uuid4

from app.core.database import AsyncSessionLocal, get_mongodb
from app.services.event_service import EventService

from app.services.temporal_service import TemporalService
from app.schemas.event_type import <PERSON><PERSON><PERSON>, EventStatus
from app.services.workflow_service import WorkFlowService
from app.utils.exceptions import NotFoundError, ValidationError, DatabaseError
from app.utils.logging import get_logger

logger = get_logger(__name__)

async def process_event(event: Dict[Any, Any]) -> None:
    """
    Process events received from AWS Lambda via RabbitMQ.

    This function implements a complete event processing pipeline:
    1. Extract event type and data from incoming event
    2. Store event in MongoDB with "in_progress" status
    3. Validate event data against event type schema
    4. If validation succeeds, trigger workflow execution
    5. Update event status at each stage

    Args:
        event: The event payload from Lambda
    """
    logger.info(f"Processing event: {event}")

    try:
        # Extract event data from the payload / only in post method
        event_data = await _extract_event_data(event)
        if not event_data:
            logger.warning("No valid event data found in payload")
            return

        # Initialize services
        mongodb = await get_mongodb()
        temporal_service = TemporalService()
        event_service = EventService(
            mongodb=mongodb,
            workflow_service=None,  # Will be initialized when needed
            temporal_service=temporal_service
        )

        event_id = await event_service.add_event(event_data)

        async with AsyncSessionLocal() as session:
            try:
                # yield session
                #TODO NKkk need to work on Event data
                workflow_service = WorkFlowService(session)
                workflow_id = "dbd8ac57-42fe-42e6-a401-d3e9b18f65af"
                # event_id.pop("created_at")
                # event_id.pop("updated_at")
                # event_id["data"].pop("timestamp")
                workflow_data = await workflow_service.start_workflow_by_id(workflow_id, event)
            except Exception as e:
                logger.error(f"Error starting workflow: {str(e)}")
            finally:
                await session.close()
            
            if event_id and workflow_data and workflow_data.get('execution_id'):
                updated_event = await event_service.update_event_running_workflow(event_id['id'], workflow_data['execution_id'], workflow_data['status'])
                event = updated_event if updated_event is not None else {}

        # final_event = await event_service.trigger_workflow(event["id"])

        logger.info(f"Event processing completed for event {event}")

    except NotFoundError as e:
        logger.error(f"Resource not found during event processing: {str(e)}")
    except ValidationError as e:
        logger.error(f"Validation error during event processing: {str(e)}")
    except DatabaseError as e:
        logger.error(f"Database error during event processing: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error processing event: {str(e)}", exc_info=True)


async def _extract_event_data(event: Dict[Any, Any]) -> Optional[EventCreate]:
    """
    Extract event data from the incoming event payload.

    This function handles different event payload formats and extracts
    the event type and data needed for processing.

    Args:
        event: The event payload from Lambda/RabbitMQ

    Returns:
        Optional[EventCreate]: Extracted event data or None if invalid
    """
    try:
        # Handle different payload formats
        body = event.get('body', '')

        # Case 1: Body is a JSON string
        if isinstance(body, str):
            try:
                body_data = json.loads(body)
            except json.JSONDecodeError:
                # Body is not JSON, treat as plain text
                body_data = {"message": body}
        else:
            # Body is already a dict/object
            body_data = body

        # Extract event type and data
        event_type = None
        event_data: Dict[str, Any] = {}

        # Try to extract from different possible structures
        if isinstance(body_data, dict):
            # Direct event structure
            if "event_type" in body_data:
                event_type = body_data["event_type"]
                data_field = body_data.get("data", body_data)
                event_data = data_field if isinstance(data_field, dict) else {"value": data_field}   
        else:
            # Non-dict body, create generic event
            event_type = "generic_event"
            event_data = {"payload": body_data}

        # Generate correlation ID from event metadata
        correlation_id = event.get('correlation_id') or str(uuid4())

        # Extract source information
        source = event.get('source', '')
        if not body_data.get('contact_id'):
            raise ValidationError("Contact ID is required")
        else:
            contact_id = str(body_data.get('contact_id'))

        # Create EventCreate object
        return EventCreate(
            event_type=event_type,
            data=event_data,
            source=source,
            correlation_id=correlation_id,
            contact_id=contact_id
        )

    except Exception as e:
        logger.error(f"Error extracting event data: {str(e)}")
        return None
