from contextlib import asynccontextmanager
from typing import Any, Dict
import re
import uuid
import async<PERSON>
from typing import Optional

from app.core.database import Async<PERSON><PERSON><PERSON>Local
from app.core.security import decrypt_credential_data, encrypt_credential_data
from app.credential.base.credential_model import <PERSON><PERSON><PERSON>eth<PERSON>, CredentialModel, CredentialRequestModel
from app.credential.utils.credential_registry import CredentialRegistry
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.config import Settings 

from app.repositories.credential_repository import CredentialRepository
from temporalio import workflow


class CredentialManager:

    def __init__(self, db: Optional[AsyncSession] = None):
        self.db = db
    @asynccontextmanager
    async def _get_session(self):
        """
        Get a database session as an async context manager.
        Uses existing session if available, otherwise creates a new one.
        """
        if self.db is not None:
            yield self.db
        else:
            async with AsyncSessionLocal() as session:
                try:
                    yield session
                finally:
                    await session.close()

    def validate_credential(self, credential_request: CredentialRequestModel):
        # credential = CredentialRegistry.get(credential_request.name)
        # if not credential:
        #     raise ValueError(f"Credential type '{credential_request.name}' not found")
        
        # if not credential.parameters:
        #     raise ValueError(f"Credential type '{credential_request.name}' has no parameters defined")
     
        # for param in credential.parameters:
        #     # Check if required parameter is present
        #     if param.required and param.name not in credential_request.parameters:
        #         raise ValueError(f"Missing required parameter: {param.name}")
                
        #     # If parameter is present, validate its type
        #     if param.name in credential_request.parameters:
        #         value = credential_request.parameters[param.name]
                
        #         # Validate type based on PropertyTypes enum
        #         self._validate_param_type(param.name, value, param.type)
                
        return True
    
    async def save_credential(self, credential_request: CredentialRequestModel, created_by: str, id: Optional[str] = None) -> Dict[str, Any]:
        """
        Save a new credential to the database.
        
        Args:
            credential_request: The credential request model with all required parameters
            
        Returns:
            Dictionary with credential information and status
        """
        if not self.db:
            raise ValueError("Database session is required for saving credentials")
        
        # Validate the credential request
        # self.validate_credential(credential_request)
        
        # Initialize repository
        credential_repo = CredentialRepository(self.db)

        # If ID is provided, update existing credential
        if id:
            try:
                uuid.UUID(id)  # Validate UUID format
            except ValueError:
                raise ValueError(f"Invalid credential ID format: {id}")
            credential = await credential_repo.get_by_id(uuid.UUID(id))
            if not credential:
                raise ValueError(f"Credential with ID {id} not found.")
            # Update existing credential
            credential_data = credential_request.model_dump(exclude_unset=True)
            credential_data['id'] = id
            credential_data['created_by'] = created_by # TODO: change to edited_by
            result = await credential_repo.update(credential, credential_data)
        else:
            # Add credential to database
            result = await credential_repo.add_credential(
                credential_request=credential_request,
                created_by="system"  # This should come from the authenticated user
            )
        
        if not result:
            raise ValueError("Failed to save credential")
        if credential_request.auth_method == AuthMethod.OAUTH2:
            cred_model = CredentialRegistry.get(credential_request.name)
            if not cred_model:
                raise ValueError(f"Credential type '{credential_request.name}' not found.")

            credential_request.parameters['credential_id'] = str(result['id']) if isinstance(result, dict) else str(result.id)
            result = await self._get_oauth2_credentials(credential_request.parameters, cred_model)

        return {
            "status": "success",
            "message": "Credential saved successfully",
            "credential": result
        }
    async def test_credential(self, id: uuid.UUID):
        if not self.db:
            raise ValueError("Database session is required for credential testing")
        credential_repo = CredentialRepository(self.db)
        credential = await credential_repo.get_by_id(id)
        if not credential:
            raise ValueError(f"Credential with ID {id} not found.")
        
        credential_details = CredentialRegistry.get(credential.type)
        if not credential_details:
            raise ValueError(f"Credential type '{credential.type}' not found.")
        from app.utils.http_client import HttpClient, HttpClientError
        try:
            credential_data = decrypt_credential_data(credential.data)
            auth_method = credential.auth_method
            if auth_method == AuthMethod.SERVICE_ACCOUNT.value:
                jwt_token = self.generate_jwt_token(credential_data, credential_details)
                access_token = await self.get_access_token_from_jwt(jwt_token, credential_details)
                if not access_token:
                    raise ValueError("Failed to retrieve access token from JWT")
                await credential_repo.update(credential, {"status": "active"})
                await self.db.commit()
                return True
            test_config = credential_details.test()
            url = credential_details.base_url + test_config.endpoint
            auth = credential_details.authentication()
            headers = self._get_placeholder_values(auth.headers, credential_data)
            query_params = self._get_placeholder_values(auth.query_params, credential_data)

            response = await HttpClient.request(
                method=test_config.method,
                url=url,
                headers=headers,
                params=query_params,
                data=test_config.body
            )

            if test_config.validation is not None:
                if not test_config.validation(response["json"]):
                    raise ValueError("Credential test failed: Invalid response data.")
            
            if credential.status != "active":
                await credential_repo.update(credential, {"status": "active"})
                await self.db.commit()
            return True
        except HttpClientError as e:
            error_msg = str(e)
            if hasattr(e, 'status_code') and e.status_code == 401:
                error_msg += " - Check if you're providing all required parameters for this API"
            raise ValueError(f"Failed to test credential: {error_msg}")

    async def get_credential(self, credential_id: str, node_type: str) -> Dict[str, Any]:
        try:
            id = uuid.UUID(credential_id)
        except ValueError:
            raise ValueError(f"Invalid credential ID format: {credential_id}")
        async with self._get_session() as db:
            credential_repo = CredentialRepository(db)
            credential = await credential_repo.get_by_id(id) # TODO: Use a more efficient query with conditions (method, status, node_type)
            if not credential:
                raise ValueError(f"Credential with ID {credential_id} not found.")
            nested_data = decrypt_credential_data(credential.data)
            flat_data = {}
            for key, details in nested_data.items():
                flat_data[key] = details["value"]
            return flat_data
            

    def _replace_placeholders(self, value: Dict[str, Any], params: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Replace placeholders in a dictionary with values from params.
        
        Args:
            value: Dictionary containing values with placeholders
            params: Dictionary containing parameter values
            
        Returns:
            Dictionary with placeholders replaced
        """
        try:
            result = value.copy()
            for key, val in result.items(): 
                if isinstance(val, str):
                    # Find all placeholders like {access_token} within the string
                    placeholders = re.findall(r"\{(.*?)\}", val)
                    for placeholder in placeholders:
                        if placeholder in params:
                            val = val.replace(f"{{{placeholder}}}", str(params[placeholder]))
                            # raise ValueError(f"Missing value for placeholder: {placeholder}")
                    result[key] = val
                elif isinstance(val, dict):
                    # Recursively replace placeholders in nested dictionaries
                    result[key] = self._replace_placeholders(val, params)
                elif isinstance(val, list):
                    # Recursively replace placeholders in lists
                    result[key] = [self._replace_placeholders(item, params) if isinstance(item, dict) else item for item in val]
                else:
                    # For other types, keep the value as is
                    result[key] = val
            return result
        except Exception as e:
            raise ValueError(f"Error replacing placeholders: {e}")
    

    def _get_placeholder_values(self, placeholder_dict: Dict[str, str], params: Dict[str, Dict[str, Any]]) -> dict[str, str]:
        result = placeholder_dict.copy()
        for key, value in result.items():
            # Find all placeholders like {access_token} within the header value
            placeholders = re.findall(r"{(.*?)}", value)

            # Replace each placeholder with its corresponding value from params
            for placeholder in placeholders:
                if placeholder in params:
                    value = value.replace(f"{{{placeholder}}}", str(params[placeholder]["value"]))
                else:
                    raise ValueError(f"Missing value for placeholder: {placeholder}")
            
            # Update the header with the replaced value
            result[key] = value

        return result
    
    async def auth_request(self, credential_id: str, url: str) -> Dict[str, Any]:
        """
        Authenticate a request using a credential.
        
        Args:
            credential_id: ID of the credential to use
            url: The original URL to make the request to
            
        Returns:
            Dict containing base_url, headers and query_params
        """
        try:
            id = uuid.UUID(credential_id)
        except ValueError:
            raise ValueError(f"Invalid credential ID format: {credential_id}")
        if not self.db:
            raise ValueError("Database session is required for credential testing")
        credential_repo = CredentialRepository(self.db)
        credential = await credential_repo.get_by_id(id)
        if not credential:
            raise ValueError(f"Credential with ID {credential_id} not found.")
        
        credential_details = CredentialRegistry.get(credential.type)
        if not credential_details:
            raise ValueError(f"Credential type '{credential.type}' not found.")

        # Extract endpoint from the original URL
        original_url_parts = url.split("://", 1)
        if len(original_url_parts) > 1:
            _, path_and_query = original_url_parts
            path_parts = path_and_query.split("/", 1)
            endpoint = "/" + path_parts[1] if len(path_parts) > 1 else "/"
        else:
            endpoint = "/"
            
        # Decrypt credential data
        credential_data = decrypt_credential_data(credential.data)

        # Get authentication headers
        auth = credential_details.authentication()
        headers = self._get_placeholder_values(auth.headers, credential_data)
        query_params = self._get_placeholder_values(auth.query_params, credential_data)

        endpoint = self._get_endpoint_placeholder_values(endpoint, credential_data)
        
        return {
            "base_url": credential_details.base_url,
            "endpoint": endpoint,
            "headers": headers,
            "query_params": query_params
        }

    def _get_endpoint_placeholder_values(self, endpoint: str, params: Dict[str, Dict[str, Any]]) -> str:
        result = endpoint
            # Find all placeholders like {access_token} within the header value
        placeholders = re.findall(r"{(.*?)}", result)

        # Replace each placeholder with its corresponding value from params
        for placeholder in placeholders:
            if placeholder.startswith("credential.") and placeholder.split(".", 1)[1] in params:
                result = result.replace(f"{{{placeholder}}}", str(params[placeholder.split(".", 1)[1]]["value"]))
            else:
                raise ValueError(f"Missing value for placeholder: {placeholder}")
        return result
    
    def _validate_param_type(self, param_name: str, value: Any, param_type_enum: str) -> None:
        """
        Validate that a parameter value matches the expected type from PropertyTypes enum.
        
        Args:
            param_name: The name of the parameter being validated
            value: The value to validate
            param_type_enum: The PropertyTypes enum value
            
        Raises:
            ValueError: If the value doesn't match the expected type
        """
        from app.node.node_base.node_models import PropertyTypes
        
        # Handle validation based on the type specified in PropertyTypes enum
        if param_type_enum == PropertyTypes.STRING:
            if not isinstance(value, str):
                raise ValueError(f"Parameter '{param_name}' must be a string, got {type(value).__name__}")
                
        elif param_type_enum == PropertyTypes.NUMBER:
            if not isinstance(value, (int, float)):
                raise ValueError(f"Parameter '{param_name}' must be a number, got {type(value).__name__}")
                
        elif param_type_enum == PropertyTypes.BOOLEAN:
            if not isinstance(value, bool):
                raise ValueError(f"Parameter '{param_name}' must be a boolean, got {type(value).__name__}")
                
        elif param_type_enum == PropertyTypes.JSON:
            if not isinstance(value, (dict, list)):
                raise ValueError(f"Parameter '{param_name}' must be a JSON object or array, got {type(value).__name__}")
        
        elif param_type_enum == PropertyTypes.OPTIONS or param_type_enum == PropertyTypes.MULTI_OPTIONS:
            # These are typically string values selected from options
            if not isinstance(value, str) and not (isinstance(value, list) and all(isinstance(item, str) for item in value)):
                raise ValueError(f"Parameter '{param_name}' must be a string or list of strings, got {type(value).__name__}")
        
        # Add more type validations as needed for other PropertyTypes
        # For now, other types will pass validation

    async def _get_oauth2_credentials(self, credential_data: Dict[str, Any], credential_details: CredentialModel) -> Dict[str, Any] | None:
        """
        Get OAuth2 credentials for a specific credential type.
        
        Args:
            credential_data: The decrypted credential data containing OAuth2 parameters
            credential_details: The credential instance from registry
            
        Returns:
            dict: The OAuth2 authorization URL and related parameters
        """
        try:
            
            # Get OAuth2 test configuration
            oauth2_test = credential_details.get_oauth2_url()
            base_url = Settings().APP_BASE_URL
            api_version = Settings().API_V1_STR
            
            # Set redirect_uri to backend callback endpoint
            redirect_uri = f"{base_url}{api_version}/credentials/callback"
            credential_data['redirect_uri'] = redirect_uri
            auth_params = self._replace_placeholders(oauth2_test.query_params, credential_data)

            import base64
            import json
            for field in oauth2_test.encode_fields:
                if field in auth_params:
                    auth_params[field] = base64.b64encode(json.dumps(auth_params[field]).encode()).decode()
            
            # Generate complete OAuth2 authorization URL
            import urllib.parse
            auth_url = auth_params.get('auth_base_url', '') + urllib.parse.urlencode({
                k: v for k, v in auth_params.items() if k != 'auth_base_url'
            })
            return {
                'authorization_url': auth_url,
                'redirect_uri': redirect_uri
            }
            
        except Exception as exc:
            raise ValueError(f"OAuth2 credential generation error: {str(exc)}")
        

    async def get_oauth2_callback(self, code: str, state: str) -> Dict[str, Any]:
        """
        Handle OAuth2 callback by identifying credential type and processing token exchange.
        
        Args:
            code: Authorization code from OAuth2 provider
            state: Base64 encoded state parameter containing credential information
            
        Returns:
            dict: Complete credential structure with tokens and user information
        """
        if not state:
            raise ValueError("State parameter is required for token exchange")

        try:
            # Decode credentials from state parameter
            import base64
            import json
            
            state_data = json.loads(base64.b64decode(state).decode())
            state_data['code'] = code
            redirect_uri = state_data['redirect_uri']
            scopes = state_data.get('scopes', '')
            credential_id = state_data.get('credential_id', '')
            credential_type = state_data.get('credential_type', '')

            credential_model = CredentialRegistry.get(credential_type)
            if not credential_model:
                raise ValueError(f"Credential type '{credential_type}' not found in registry")
            oauth2_token = credential_model.get_oauth2_token()
            if not oauth2_token:
                raise ValueError(f"OAuth2 token configuration not found for credential type '{credential_type}'")
            
            token_data = self._replace_placeholders(oauth2_token.query_params, state_data)
            token_url = oauth2_token.endpoint
            if not token_url:
                raise ValueError("Token endpoint URL is missing in OAuth2 configuration")

            # Exchange code for tokens using authentication config
            from app.utils.http_client import HttpClient, HttpMethod, HttpClientError
            try:
                response = await HttpClient.request(
                    method=HttpMethod.POST,
                    url=token_url,
                    data=token_data
                )
            except HttpClientError as e:
                error_msg = str(e)
                if hasattr(e, 'status_code'):
                    if e.status_code == 400:
                        error_msg += " - Invalid authorization code or expired"
                    elif e.status_code == 401:
                        error_msg += " - Invalid client credentials"
                    elif e.status_code == 403:
                        error_msg += " - Access forbidden"
                raise ValueError(f"Failed to exchange authorization code for tokens: {error_msg}")
            oauth_token = response['json']
            # Append additional OAuth data to the token response
            oauth_token['redirect_uri'] = redirect_uri
            if not self.db:
                raise ValueError("Database session is required for credential testing")
            credential_repo = CredentialRepository(self.db)
            credential = await credential_repo.get_by_id(uuid.UUID(credential_id))
            if not credential:
                raise ValueError(f"Credential with ID {credential_id} not found.")
            encrypted_data = self.update_credential_data(credential.data, oauth_token)
            await credential_repo.update(credential, {"status": "active", "data": encrypted_data})
            await self.db.commit()    
            return {
                'status': 'success',
                'tokens': oauth_token,
                'scopes': scopes,
                'message': 'OAuth2 callback processed successfully'
            }
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid state parameter format: {e}")
        except KeyError as e:
            raise ValueError(f"Missing required parameter in state: {e}")
        except Exception as e:
            raise ValueError(f"OAuth2 callback error: {str(e)}")


    def update_credential_data(self, credential_data: str, updated_credential: Dict[str, Any]) -> str:
        """
        Update credential data by merging with existing data.
        Existing parameters are preserved, new parameters are added.
        
        Args:
            credential_data: Encrypted credential data as string
            new_data: New parameters to add/update
            
        Returns:
            Updated encrypted credential data as string
            
        Raises:
            ValueError: If decryption/encryption fails or invalid data provided
        """
        if not credential_data:
            raise ValueError("Credential data cannot be empty")
        
        if not updated_credential:
            # No new data to merge, return original
            return credential_data
        
        try:
            # Decrypt existing data
            existing_data = decrypt_credential_data(credential_data)
            # Validate existing data structure
            if not isinstance(existing_data, dict):
                raise ValueError("Invalid credential data structure")
            
            # Create updated data by merging new parameters
            updated_data = existing_data.copy()
            
            # Process new parameters more efficiently
            for param_name, param_value in updated_credential.items():
                # Only add if parameter doesn't exist or has changed
                if param_name not in existing_data:
                    updated_data[param_name] = {
                        "value": param_value,
                        "is_sensitive": True
                    }
                elif existing_data[param_name].get("value") != param_value:
                    # Update existing parameter if value has changed
                    updated_data[param_name] = {
                        "value": param_value,
                        "is_sensitive": existing_data[param_name].get("is_sensitive", True)
                    }
            # Only encrypt if data actually changed
            if updated_data != existing_data:
                return encrypt_credential_data(updated_data)
            
            # Return original if no changes
            return credential_data
            
        except Exception as e:
            raise ValueError(f"Failed to update credential data: {str(e)}")
        

    def generate_jwt_token(self, credential_data: Dict[str, Any], credential_details: CredentialModel) -> str:
        """
        Create JWT for service account authentication.
        
        Args:
            config: Gmail connection configuration
            
        Returns:
            str: JWT token
            
        Raises:
            ValueError: If JWT creation fails
        """
        try:
            # Get OAuth2 test configuration
            jwt_payload = credential_details.generate_jwt_payload()
            credential_data = {k: v["value"] for k, v in credential_data.items()}
            payload = self._replace_placeholders(jwt_payload.query_params, credential_data)
            # Add impersonation if required
            is_impersonate = credential_data.get('is_impersonate_user', {})
            impersonate_email = credential_data.get('impersonate_user_email', {})
            if is_impersonate and impersonate_email:
                payload['sub'] = impersonate_email
            
                import base64
                import jwt
                # Clean up private key format
                service_acc_private_key = credential_data.get('service_acc_private_key', {})
                private_key = service_acc_private_key
                if not private_key.startswith('-----BEGIN'):
                    # Handle base64 encoded keys
                    try:
                        private_key = base64.b64decode(private_key).decode('utf-8')
                    except:
                        pass
                
                # Create JWT
                jwt_token = jwt.encode(payload, private_key, algorithm='RS256')
            return jwt_token
            
        except Exception as e:
            raise ValueError(f"Failed to create service account JWT: {str(e)}")

    async def get_access_token_from_jwt(self, jwt_token: str, credential_details: CredentialModel) -> str:
        """
        Get access token from JWT for service account authentication.
        
        Args:
            jwt_token: JWT token string
            
        Returns:
            str: Access token
            
        Raises:
            ValueError: If access token retrieval fails
        """
        try:
            # Get OAuth2 token endpoint
            auth_token = credential_details.get_auth_with_jwt()
            if not auth_token or not auth_token.endpoint:
                raise ValueError("OAuth2 token endpoint is not configured")
            cred_data : Dict[str, Any] = {
                "jwt_token": jwt_token,  
            }
            # Prepare request data
            token_data = self._replace_placeholders(auth_token.query_params, cred_data)
            # URL encode the data for form submission
            import urllib.parse
            # encoded_data = urllib.parse.urlencode(token_data)
            from app.utils.http_client import HttpClient, HttpMethod, HttpClientError
            try:
                response = await HttpClient.request(
                    method=HttpMethod.POST,
                    url=auth_token.endpoint,
                    data=token_data
                )
                
                # Check if response contains the expected data
                if not response or 'json' not in response:
                    raise ValueError("Invalid response format from token endpoint")
                
                response_data = response['json']
                # Check for access_token in response
                if 'access_token' not in response_data:
                    # Handle error response from OAuth server
                    error_description = response_data.get('error_description', 'Unknown error')
                    error_code = response_data.get('error', 'invalid_request')
                    raise ValueError(f"Token exchange failed: {error_code} - {error_description}")
                
                return response_data['access_token']
            
            except HttpClientError as e:
                # Handle specific HTTP errors
                error_msg = f"HTTP request failed: {str(e)}"
            
                if hasattr(e, 'status_code'):
                    if e.status_code == 400:
                        error_msg = "Bad Request - Invalid JWT assertion or malformed request parameters"
                    elif e.status_code == 401:
                        error_msg = "Unauthorized - Invalid service account credentials or expired JWT"
                    elif e.status_code == 403:
                        error_msg = "Forbidden - Service account does not have required permissions"
                    elif e.status_code == 404:
                        error_msg = "Not Found - Invalid token endpoint URL"
                    elif e.status_code == 429:
                        error_msg = "Rate Limited - Too many token requests, please retry later"
                    elif e.status_code == 500:
                        error_msg = f"Server Error ({e.status_code}) - OAuth provider is experiencing issues"
                    else:
                        error_msg = f"HTTP Error ({e.status_code}) - {str(e)}"
                raise ValueError(error_msg)
        
        except ValueError as e:
            raise ValueError(f"Failed to retrieve access token from JWT: {str(e)}")

        except Exception as e:
            raise ValueError(f"Error getting access token from JWT: {str(e)}")