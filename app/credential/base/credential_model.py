from abc import abstractmethod
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Union
import warnings

from pydantic import BaseModel

from app.node.node_base.node_models import NodeParameter, NodeParameterValue

# Suppress deprecation warnings from pydantic
warnings.filterwarnings("ignore", category=DeprecationWarning, module="pydantic")

class AuthMethod(Enum):
    BEARER = "Bearer"
    JWT = "JWT"
    BASIC = "Basic"
    API_KEY = "API_KEY"
    OAUTH2 = "OAuth2"
    CUSTOM = "Custom"
    SERVICE_ACCOUNT = "Service Account"

class CredentialAuthModel(BaseModel):
    method: AuthMethod
    headers: Dict[str, str] = {}
    endpoint: Optional[str] = None
    query_params: Dict[str, Any] = {}
    encode_fields: List[str] = []

class CredentialTestModel(BaseModel):
    endpoint: str
    method: str = "GET"
    query_params: Dict[str, str] = {}
    body: Optional[Dict[str, str]] = None
    validation: Optional[Callable[[dict], bool]] = None

class CredentialModel(BaseModel):
    name: str = ""
    display_name: str = ""
    description: str = ""
    icon: Optional[str] = None
    icon_color: Optional[str] = None
    icon_url: Optional[str] = None
    documentation_url: Optional[str] = None
    subtitle: Optional[str] = None
    version: Union[float, List[float]] = 1.0
    parameters: Optional[List[NodeParameter]] = None
    allowed_nodes: List[str] = []
    base_url: str = ""
    auth_methods: Optional[List[AuthMethod]] = None


    def dict(self, *args, **kwargs):
        """
        Return dict representation with nulls and empty containers filtered out
        """
        try:
            return super().model_dump(*args, **kwargs, exclude_none=True)
        except AttributeError:
           return super().dict(*args, **kwargs, exclude_none=True)
        
    @abstractmethod
    def authentication(self) -> CredentialAuthModel:
        """
        Authenticate the credential using the provided parameters.
        This method should be implemented by subclasses.
        """
        raise NotImplementedError("Subclasses must implement this method.")
    
    @abstractmethod
    def test(self) -> CredentialTestModel:
        raise NotImplementedError("Subclasses must implement this method.")
    
    def get_oauth2_url(self) -> CredentialAuthModel:
        raise NotImplementedError("Subclasses must implement this method.")
    
    def get_oauth2_token(self) -> CredentialAuthModel:
        raise NotImplementedError("Subclasses must implement this method.")
    
    def generate_jwt_payload(self) -> CredentialAuthModel:
        raise NotImplementedError("Subclasses must implement this method.")
    
    def get_auth_with_jwt(self) -> CredentialAuthModel:
        raise NotImplementedError("Subclasses must implement this method.")
  
class CredentialRequestModel(BaseModel):
    # id: Optional[str] = None
    name: str
    display_name: str
    version: Union[float, List[float]]
    parameters: Dict[str, NodeParameterValue] = {}
    auth_method: Optional[AuthMethod] = None
    