"""
Gmail Credential Model

This module defines the Gmail credential model for OAuth2 authentication
with the Gmail API, following the project's established credential patterns.
"""

import urllib.parse
import aiohttp
import json
import base64
from typing import Dict, Any, Optional
from app.credential.base.credential_model import (
    AuthMethod,
    CredentialAuthModel,
    CredentialModel,
    CredentialTestModel
)
from app.credential.utils.credential_registry import credential_provider
from app.node.node_base.node_models import CalloutAction, DisplayOptions, NodeParameter, PropertyTypeOptions, PropertyTypes, NodeParameterOption, NodeRequest


@credential_provider(name="gmail")
class GmailCredential(CredentialModel):
    """
    Gmail API credential model for OAuth2 authentication.
    
    Supports both OAuth2 flow and service account authentication
    for accessing Gmail API endpoints.
    """
    
    name: str = "gmail"
    display_name: str = "Gmail API"
    description: str = "Gmail API credentials for email operations"
    icon: str = "gmail"
    icon_color: str = "#EA4335"
    icon_url: str = "https://developers.google.com/gmail/images/gmail-api-logo.png"
    documentation_url: str = "https://developers.google.com/gmail/api"
    subtitle: str = "Gmail API OAuth2 credentials"
    version: float = 1.0
    allowed_nodes: list[str] = ["gmail"]
    base_url: str = "https://gmail.googleapis.com/gmail/v1"
    auth_methods: list[AuthMethod] = [AuthMethod.OAUTH2, AuthMethod.SERVICE_ACCOUNT]

    parameters: list[NodeParameter] = [
        NodeParameter(
            name="authentication_method",
            display_name="Authentication Method",
            description="The method to use for gmail authentication",
            type=PropertyTypes.TAB,
            required=True,
            default="oauth2",
            options=[
                NodeParameterOption(name="OAuth2", value="oauth2"),
                NodeParameterOption(name="Service Account", value="service_account"),
            ]
        ),
        NodeParameter(
            name="gcp_region",
            display_name="Region",
            description="The Google Cloud Platform region for the Gmail API",
            type=PropertyTypes.OPTIONS,
            required=True,
            default="us-central1",
            display_options=DisplayOptions(
                        show={
                            "authentication_method": ["service_account"]
                        }
                    ),
            options=[
                # Americas
                NodeParameterOption(name="Americas (Council Bluffs)", value="us-central1"),
                NodeParameterOption(name="Americas (South Carolina)", value="us-east1"),
                NodeParameterOption(name="Americas (Ashburn)", value="us-east4"),
                NodeParameterOption(name="Americas (Columbus)", value="us-east5"),
                NodeParameterOption(name="Americas (Oregon)", value="us-west1"),
                NodeParameterOption(name="Americas (Los Angeles)", value="us-west2"),
                NodeParameterOption(name="Americas (Salt Lake City)", value="us-west3"),
                NodeParameterOption(name="Americas (Las Vegas)", value="us-west4"),
                NodeParameterOption(name="Americas (Dallas)", value="us-south1"),
                NodeParameterOption(name="Americas (Montréal)", value="northamerica-northeast1"),
                NodeParameterOption(name="Americas (Toronto)", value="northamerica-northeast2"),
                NodeParameterOption(name="Americas (São Paulo)", value="southamerica-east1"),
                NodeParameterOption(name="Americas (Santiago)", value="southamerica-west1"),

                # Europe
                NodeParameterOption(name="Europe (St. Ghislain)", value="europe-west1"),
                NodeParameterOption(name="Europe (London)", value="europe-west2"),
                NodeParameterOption(name="Europe (Frankfurt)", value="europe-west3"),
                NodeParameterOption(name="Europe (Eemshaven)", value="europe-west4"),
                NodeParameterOption(name="Europe (Zurich)", value="europe-west6"),
                NodeParameterOption(name="Europe (Milan)", value="europe-west8"),
                NodeParameterOption(name="Europe (Paris)", value="europe-west9"),
                NodeParameterOption(name="Europe (Berlin)", value="europe-west10"),
                NodeParameterOption(name="Europe (Turin)", value="europe-west12"),
                NodeParameterOption(name="Europe (Warsaw)", value="europe-central2"),
                NodeParameterOption(name="Europe (Hamina)", value="europe-north1"),
                NodeParameterOption(name="Europe (Madrid)", value="europe-southwest1"),

                # Asia Pacific
                NodeParameterOption(name="Asia (Taiwan)", value="asia-east1"),
                NodeParameterOption(name="Asia (Hong Kong)", value="asia-east2"),
                NodeParameterOption(name="Asia (Tokyo)", value="asia-northeast1"),
                NodeParameterOption(name="Asia (Osaka)", value="asia-northeast2"),
                NodeParameterOption(name="Asia (Seoul)", value="asia-northeast3"),
                NodeParameterOption(name="Asia (Mumbai)", value="asia-south1"),
                NodeParameterOption(name="Asia (Delhi)", value="asia-south2"),
                NodeParameterOption(name="Asia (Singapore)", value="asia-southeast1"),
                NodeParameterOption(name="Asia (Jakarta)", value="asia-southeast2"),

                # Australia & Africa
                NodeParameterOption(name="Australia (Sydney)", value="australia-southeast1"),
                NodeParameterOption(name="Australia (Melbourne)", value="australia-southeast2"),
                NodeParameterOption(name="Africa (Johannesburg)", value="africa-south1"),

                # Middle East
                NodeParameterOption(name="Middle East (Tel Aviv)", value="me-west1"),
                NodeParameterOption(name="Middle East (Doha)", value="me-central1"),
                NodeParameterOption(name="Middle East (Dammam)", value="me-central2"),
            ]
        ),
        NodeParameter(
            name="service_acc_email",
            display_name="Service Account Email",
            description="Email address of the service account for the Gmail API",
            type=PropertyTypes.STRING,
            required=True,
            placeholder="<EMAIL>",
            display_options=DisplayOptions(
                show={"authentication_method": ["service_account"]}
            )
        ),
        NodeParameter(
            name="service_acc_private_key",
            display_name="Service Account Private Key",
            description="Private key for the service account",
            type=PropertyTypes.STRING,
            required=True,
            sensitive=True,
            display_options=DisplayOptions(
                show={"authentication_method": ["service_account"]}
            )
        ),
        NodeParameter(
            name="is_impersonate_user",
            display_name="Impersonate a User",
            description="Whether to impersonate a user",
            type=PropertyTypes.BOOLEAN,
            required=False,
            default=False,
            display_options=DisplayOptions(
                show={"authentication_method": ["service_account"]}
            )
        ),
        NodeParameter(
            name="impersonate_user_email",
            display_name="Impersonate User Email",
            description="Email address of the user to impersonate",
            type=PropertyTypes.STRING,
            required=True,
            placeholder="<EMAIL>",
            display_options=DisplayOptions(
                show={
                    "authentication_method": ["service_account"],
                    "is_impersonate_user": [True]
                }
            )
        ),

        NodeParameter(
            name="client_id",
            display_name="Client ID",
            description="OAuth2 client ID from Google Cloud Console",
            type=PropertyTypes.STRING,
            required=True,
            placeholder="*********-abcdefghijklmnop.apps.googleusercontent.com",
            display_options=DisplayOptions(
                show={"authentication_method": ["oauth2"]}
            )
        ),
        NodeParameter(
            name="client_secret",
            display_name="Client Secret",
            description="OAuth2 client secret from Google Cloud Console",
            type=PropertyTypes.STRING,
            required=True,
            sensitive=True,
            placeholder="GOCSPX-abcdefghijklmnopqrstuvwxyz",
            display_options=DisplayOptions(
                show={"authentication_method": ["oauth2"]}
            )
        ),
        NodeParameter(
            name="google_oauth2_button",
            display_name="Google OAuth2 Button",
            description="Button to initiate Google OAuth2 flow",
            type=PropertyTypes.OAUTH2_BUTTON,
            required=False,
            display_options=DisplayOptions(
                show={"authentication_method": ["oauth2"]}
            )
        )
    ]

    def authentication(self) -> CredentialAuthModel:
        """
        Configure OAuth2 Bearer token authentication for Gmail API.
        
        Returns:
            CredentialAuthModel: Authentication configuration
        """
        return CredentialAuthModel(
            method=AuthMethod.BEARER,
            headers={"Authorization": "Bearer {access_token}"},
            query_params={}
        )
    
    def test(self) -> CredentialTestModel:
        """
        Configure credential test endpoint for Gmail API.

        Returns:
            CredentialTestModel: Test configuration
        """
        return CredentialTestModel(
            endpoint="/users/me/profile",
            method="GET",
            validation=lambda response: (
                'emailAddress' in response and
                response['emailAddress'] is not None and
                '@' in response['emailAddress']
            )
        )

    def get_oauth2_url(self) -> CredentialAuthModel:
        """
        Configure OAuth2 test endpoint for Gmail API.

        Returns:
            CredentialAuthModel: OAuth2 authorization urlconfiguration
        """
        # Use Google's OAuth2 authorization endpoint
        
        auth_base_url = "https://accounts.google.com/o/oauth2/v2/auth?"
        scopes = [
            "https://www.googleapis.com/auth/gmail.send",
            "https://www.googleapis.com/auth/gmail.readonly",
            "https://www.googleapis.com/auth/gmail.modify"
        ]
        # scopes = [
        #     "https://www.googleapis.com/auth/userinfo.email",
        #     "https://www.googleapis.com/auth/userinfo.profile",
        #     "openid"
        # ]
        # Ensure scopes is a string (handle both string and array inputs)
        if isinstance(scopes, list):
            scopes = ' '.join(scopes)
        state = {
            "client_id": "{client_id}",
            "client_secret": "{client_secret}",
            "redirect_uri": "{redirect_uri}",
            "scopes": scopes,
            "credential_type": "gmail",
            "credential_id": "{credential_id}"
            # "authentication_method": "oauth2"
        }
        return CredentialAuthModel(
            method=AuthMethod.OAUTH2,
            query_params = {"auth_base_url": auth_base_url, "redirect_uri": "{redirect_uri}", "scope": scopes, "response_type": "code", "access_type": "offline", "prompt": "consent", "state": state, "client_id": "{client_id}"},
            encode_fields=["state"],
        )

    def get_oauth2_token(self) -> CredentialAuthModel:
        """
        Get OAuth2 callback token configuration for Gmail API.

        Returns:
            CredentialAuthModel: OAuth2 callback token configuration
        """
        token_url = "https://oauth2.googleapis.com/token"
        return CredentialAuthModel(
            method=AuthMethod.OAUTH2,
            endpoint=token_url,
            query_params={"client_id": "{client_id}", "client_secret": "{client_secret}", "code": "{code}", "grant_type": "authorization_code", "redirect_uri": "{redirect_uri}"},
        )

    def generate_jwt_payload(self) -> CredentialAuthModel:
        """
        Get service account JWT token configuration for Gmail API.

        Returns:
            CredentialAuthModel: Service account JWT token configuration
        """
        scopes = [
            "https://www.googleapis.com/auth/gmail.send" #TO DO: Multiple scope is not suppporting, need to check
            # "https://www.googleapis.com/auth/gmail.readonly"
            # "https://www.googleapis.com/auth/gmail.modify"
        ]
        if isinstance(scopes, list):
            scopes = ' '.join(scopes)
    
        token_url = "https://oauth2.googleapis.com/token"
        
        from datetime import timedelta, datetime, timezone

        # now = datetime.now(timezone.utc)
        # expiration_time = now + timedelta(hours=1),  # Token valid for 1 hour
        now = datetime.now(timezone.utc)
        expiration_time = now + timedelta(hours=1)  # Token valid for 1 hour
        
        # Convert to Unix timestamps for JWT
        now_timestamp = int(now.timestamp())
        exp_timestamp = int(expiration_time.timestamp())
        # Prepare JWT payload
        return CredentialAuthModel(
            method=AuthMethod.SERVICE_ACCOUNT,
            endpoint=token_url,
            query_params={"iss": "{service_acc_email}", "scope": scopes, "aud": token_url, "exp": exp_timestamp, "iat": now_timestamp}
        )
    
    def get_auth_with_jwt(self) -> CredentialAuthModel:
        """
        Get access token configuration for Gmail API.

        Returns:
            CredentialAuthModel: Access token configuration
        """
        token_url = "https://oauth2.googleapis.com/token"
        grant_type = "urn:ietf:params:oauth:grant-type:jwt-bearer"
        return CredentialAuthModel(
            method=AuthMethod.SERVICE_ACCOUNT,
            endpoint=token_url,
            query_params={"grant_type": grant_type, "assertion": "{jwt_token}"}
        )
