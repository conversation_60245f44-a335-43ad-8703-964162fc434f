from app.node.node_base.node_models import NodeConnectionType, NodeParameter, NodeRequest, NodeTypeDescription, PropertyTypes, ValidationResult
from app.node.node_utils.workflow_defn import node_defn
from app.node.node_base.node import Node, NodeResult

@node_defn(type='manual_trigger', is_activity=False)
class ManualTriggerNode(Node):
    """Node that triggers a workflow manually."""

    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        return NodeTypeDescription(
            name="manual_trigger",
            display_name="Manual Trigger",
            description="Runs the flow on clicking a button in saaya.",
            icon="fa:mouse-pointer",
            icon_color="#909298",
            group=["trigger"],
            version=1,
            inputs=[],
            outputs=[NodeConnectionType.Main],
            parameters=[
                NodeParameter(
                    name="notice",
                    display_name="Notice",
                    type=PropertyTypes.NOTICE,
                    default="This node is where the workflow execution starts (when you click the ‘test’ button on the canvas).<br><br> <a data-action='showNodeCreator'>Explore other ways to trigger your workflow</a> (e.g on a schedule, or a webhook)"
                )
            ],
        )
    
    async def run(self, request: NodeRequest) -> NodeResult:
        """ Run the manual trigger node.
        This node does not perform any action but serves as a starting point for the workflow.
        """
        return NodeResult(
            next_connection_index=0
        )
    
    def validate(self, request: NodeRequest) -> ValidationResult:
        """
        Validate the node request.
        This node does not require any specific validation.
        """
        return ValidationResult(
            valid=True,
            errors=[],
        )
