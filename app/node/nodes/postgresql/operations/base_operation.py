"""
Base Operation Module

This module provides base functionality and utilities for PostgreSQL operations
including connection management, error handling, and common database utilities.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Union
from contextlib import asynccontextmanager

import asyncpg
from asyncpg import Connection, Pool
from pydantic import BaseModel, Field

from app.node.node_base.node import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.node.node_base.node_models import NodeData

# Set up logging
logger = logging.getLogger(__name__)


class PostgreSQLConnectionConfig(BaseModel):
    """Configuration model for PostgreSQL connections."""
    
    host: str = Field(..., description="Database host")
    port: int = Field(5432, description="Database port")
    database: str = Field(..., description="Database name")
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")
    ssl_mode: str = Field("prefer", description="SSL mode")
    connection_timeout: int = Field(30, description="Connection timeout in seconds")
    max_connections: int = Field(10, description="Maximum connections in pool")
    allow_unauthorized_certs: bool = Field(False, description="Allow unauthorized certificates")


class PostgreSQLConnectionManager:
    """
    PostgreSQL connection manager with connection pooling.
    
    Provides secure connection management, connection pooling,
    and proper resource cleanup for PostgreSQL operations.
    """
    
    _pools: Dict[str, Pool] = {}
    
    @classmethod
    async def get_connection_config(cls, data: NodeData) -> PostgreSQLConnectionConfig:
        """
        Extract PostgreSQL connection configuration from node data.
        
        Args:
            data: Node execution data containing credentials
            
        Returns:
            PostgreSQLConnectionConfig: Connection configuration
            
        Raises:
            ValueError: If credentials are missing or invalid
        """
        if not data.credentials or 'postgresql' not in data.credentials:
            raise ValueError("PostgreSQL credentials are required")
        
        cred = data.credentials['postgresql']
        try:
            # Assuming cred is a dictionary with the necessary fields
            from app.node.nodes.postgresql.postgresql_node import PostgreSQLNode
            cred_data = await PostgreSQLNode().get_credential(cred.id) #TODO: Adjust based on actual credential retrieval logic
            if cred_data is None:
                raise ValueError("Credential data not found or is None")
        except Exception as e:
            logger.error(f"Failed to extract PostgreSQL credentials: {str(e)}")
            raise ValueError("Invalid PostgreSQL credential format")
        
        return PostgreSQLConnectionConfig(
            host=cred_data.get('host', 'localhost'),
            port=int(cred_data.get('port', 5432)),
            database=cred_data.get('database', ''),
            username=cred_data.get('username', ''),
            password=cred_data.get('password', ''),
            ssl_mode=cred_data.get('ssl_mode', 'prefer'),
            connection_timeout=int(cred_data.get('connection_timeout', 30)),
            max_connections=int(cred_data.get('max_connections', 10)),
            allow_unauthorized_certs=bool(cred_data.get('allow_unauthorized_certs', False))
        )
    
    @classmethod
    async def get_pool(cls, config: PostgreSQLConnectionConfig) -> Pool:
        """
        Get or create a connection pool for the given configuration.
        
        Args:
            config: PostgreSQL connection configuration
            
        Returns:
            Pool: AsyncPG connection pool
        """
        # Create a unique key for this configuration
        pool_key = f"{config.host}:{config.port}:{config.database}:{config.username}"
        
        if pool_key not in cls._pools or cls._pools[pool_key].is_closing():
            # Create SSL context based on ssl_mode
            ssl_context = None
            if config.ssl_mode != 'disable':
                import ssl
                ssl_context = ssl.create_default_context()
                if config.allow_unauthorized_certs:
                    ssl_context.check_hostname = False
                    ssl_context.verify_mode = ssl.CERT_NONE
            
            # Create connection pool
            cls._pools[pool_key] = await asyncpg.create_pool(
                host=config.host,
                port=config.port,
                database=config.database,
                user=config.username,
                password=config.password,
                ssl=ssl_context,
                min_size=1,
                max_size=config.max_connections,
                command_timeout=config.connection_timeout,
                server_settings={
                    'application_name': 'cerebro_postgresql_node'
                }
            )
            
            logger.info(f"Created PostgreSQL connection pool for {config.host}:{config.port}/{config.database}")
        
        return cls._pools[pool_key]
    
    @classmethod
    @asynccontextmanager
    async def get_connection(cls, data: NodeData):
        """
        Get a database connection from the pool.
        
        Args:
            data: Node execution data
            
        Yields:
            Connection: PostgreSQL connection
        """
        config = await cls.get_connection_config(data)
        pool = await cls.get_pool(config)
        
        async with pool.acquire() as connection:
            try:
                yield connection
            except Exception as e:
                logger.error(f"Database operation failed: {str(e)}")
                raise
    
    @classmethod
    async def close_all_pools(cls):
        """Close all connection pools."""
        for pool_key, pool in cls._pools.items():
            if not pool.is_closing():
                await pool.close()
                logger.info(f"Closed connection pool: {pool_key}")
        cls._pools.clear()


class BaseOperation:
    """
    Base class for PostgreSQL operations.
    
    Provides common functionality for database operations including
    error handling, parameter validation, and result formatting.
    """
    
    @staticmethod
    def format_error(error: Exception, operation: str) -> str:
        """
        Format database errors into user-friendly messages.
        
        Args:
            error: The exception that occurred
            operation: The operation being performed
            
        Returns:
            str: Formatted error message
        """
        if isinstance(error, asyncpg.PostgresError):
            return f"PostgreSQL {operation} error: {str(error)}"
        # elif isinstance(error, asyncpg.ConnectionError):
        #     return f"PostgreSQL connection error during {operation}: {str(error)}"
        elif isinstance(error, asyncio.TimeoutError):
            return f"PostgreSQL {operation} operation timed out"
        else:
            return f"PostgreSQL {operation} operation failed: {str(error)}"
    
    @staticmethod
    def format_results(records: List[Any], operation: str) -> Dict[str, Any]:
        """
        Format database results into a standardized structure.
        
        Args:
            records: Database query results
            operation: The operation that was performed
            
        Returns:
            Dict containing formatted results
        """
        if not records:
            return {
                "operation": operation,
                "rows_affected": 0,
                "data": []
            }
        
        # Convert asyncpg.Record objects to dictionaries
        formatted_records = []
        for record in records:
            if hasattr(record, 'items'):
                # asyncpg.Record has items() method
                formatted_records.append(dict(record))
            elif isinstance(record, dict):
                formatted_records.append(record)
            else:
                # Handle other types
                formatted_records.append(record)
        
        return {
            "operation": operation,
            "rows_affected": len(formatted_records),
            "data": formatted_records
        }
    
    @staticmethod
    def validate_table_name(table_name: str) -> bool:
        """
        Validate table name for SQL injection prevention.
        
        Args:
            table_name: Table name to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not table_name or not isinstance(table_name, str):
            return False
        
        # Basic validation - alphanumeric, underscore, and dots for schema.table
        import re
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$'
        return bool(re.match(pattern, table_name))
    
    @staticmethod
    def escape_identifier(identifier: str) -> str:
        """
        Escape SQL identifiers to prevent injection.
        
        Args:
            identifier: SQL identifier to escape
            
        Returns:
            str: Escaped identifier
        """
        # Remove any existing quotes and add double quotes
        escaped = identifier.replace('"', '""')
        return f'"{escaped}"'
