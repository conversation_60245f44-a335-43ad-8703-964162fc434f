"""
Gmail Node Model

This module defines the Gmail node model with comprehensive parameter definitions
for all Gmail operations following established patterns.
"""

from typing import List, Optional, Any, Dict
from pydantic import BaseModel, Field

from app.node.node_base.node_models import (
    NodeTypeDescription,
    NodeParameter,
    PropertyTypes,
    NodeCredentialDescription,
    DisplayOptions,
    NodeParameterOption
)


class GmailNodeDescription:
    """Gmail node description with comprehensive parameter definitions."""
    
    @staticmethod
    def create() -> NodeTypeDescription:
        """Create the Gmail node type description."""
        return NodeTypeDescription(
            name="Gmail",
            display_name="Gmail",
            description="Gmail integration for email operations including sending, reading, and managing emails and labels",
            group=["output"],
            version=1.0,
            parameters=GmailNodeDescription._get_parameters(),
            credentials=[GmailNodeDescription._get_credential_description()]
        )
    
    @staticmethod
    def _get_parameters() -> List[NodeParameter]:
        """Get all Gmail node parameters."""
        return [
            # Operation selection
            NodeParameter(
                name="operation",
                type=PropertyTypes.OPTIONS,
                description="Gmail operation to perform",
                required=True,
                default="send_email",
                options=[
                NodeParameterOption(value="send_email", name="Send Email"),
                NodeParameterOption(value="read_emails", name="Read Emails"),
                NodeParameterOption(value="mark_read", name="Mark Read"),
                NodeParameterOption(value="mark_unread", name="Mark Unread"),
                NodeParameterOption(value="delete_email", name="Delete Email"),
                NodeParameterOption(value="move_email", name="Move Email"),
                NodeParameterOption(value="create_label", name="Create Label"),
                NodeParameterOption(value="list_labels", name="List Labels"),
                NodeParameterOption(value="add_label", name="Add Label"),
                NodeParameterOption(value="remove_label", name="Remove Label")
            ]
            ),
            # display_options=DisplayOptions(
            #         show={"operation": ["send_email"]},
            #     ),

            # Send Email Parameters
            NodeParameter(
                name="to",
                type=PropertyTypes.STRING,
                description="Recipient email addresses (comma-separated)",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["send_email"]},
                ),
                
            ),
            NodeParameter(
                name="cc",
                type=PropertyTypes.STRING,
                description="CC email addresses (comma-separated)",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["send_email"]},
                ),
            ),
            NodeParameter(
                name="bcc",
                type=PropertyTypes.STRING,
                description="BCC email addresses (comma-separated)",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["send_email"]},
                ),
            ),
            NodeParameter(
                name="subject",
                type=PropertyTypes.STRING,
                description="Email subject",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["send_email"]},
                ),
            ),
            NodeParameter(
                name="body_text",
                type=PropertyTypes.STRING,
                description="Plain text email body",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["send_email"]},
                ),
            ),
            NodeParameter(
                name="body_html",
                type=PropertyTypes.STRING,
                description="HTML email body",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["send_email"]},
                ),
            ),
            NodeParameter(
                name="attachments",
                type=PropertyTypes.JSON,
                description="Email attachments as JSON array with filename and content",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["send_email"]},
                ),
            ),

            # Read Emails Parameters
            NodeParameter(
                name="query",
                type=PropertyTypes.STRING,
                description="Gmail search query (e.g., 'from:<EMAIL> is:unread')",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["read_emails"]},
                ),
            ),
            NodeParameter(
                name="max_results",
                type=PropertyTypes.NUMBER,
                description="Maximum number of emails to retrieve (1-500)",
                required=False,
                default=10,
                display_options=DisplayOptions(
                    show={"operation": ["read_emails"]},
                ),
            ),
            NodeParameter(
                name="format",
                type=PropertyTypes.OPTIONS,
                description="Email format to retrieve",
                required=False,
                default="metadata",
                options=[
                    NodeParameterOption(value="metadata", name="Metadata Only"),
                    NodeParameterOption(value="minimal", name="Minimal"),
                    NodeParameterOption(value="full", name="Full Content")
                ],
                display_options=DisplayOptions(
                    show={"operation": ["read_emails"]},
                ),
            ),
            NodeParameter(
                name="include_spam_trash",
                type=PropertyTypes.BOOLEAN,
                description="Include emails from spam and trash",
                required=False,
                default=False,
                display_options=DisplayOptions(
                    show={"operation": ["read_emails"]},
                ),
            ),

            # Message Management Parameters
            NodeParameter(
                name="message_ids",
                type=PropertyTypes.STRING,
                description="Message IDs (comma-separated)",
                required=False,
                display_options=DisplayOptions(
                    show={
                        "operation": [
                            "mark_read",
                            "mark_unread", 
                            "delete_email",
                            "move_email",
                            "add_label",
                            "remove_label"
                        ]
                    },
                ),
            ),

            # Delete Email Parameters
            NodeParameter(
                name="permanent",
                type=PropertyTypes.BOOLEAN,
                description="Permanently delete (true) or move to trash (false)",
                required=False,
                default=False,
                display_options=DisplayOptions(
                    show={"operation": ["delete_email"]},
                ),
            ),

            # Move Email Parameters
            NodeParameter(
                name="add_labels",
                type=PropertyTypes.STRING,
                description="Label IDs to add (comma-separated)",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["move_email"]},
                ),
            ),
            NodeParameter(
                name="remove_labels",
                type=PropertyTypes.STRING,
                description="Label IDs to remove (comma-separated)",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["move_email"]},
            ),
            ),
            NodeParameter(
                name="destination_folder",
                type=PropertyTypes.OPTIONS,
                description="Destination folder",
                required=False,
                options=[
                    NodeParameterOption(value="inbox", name="Inbox"),
                    NodeParameterOption(value="sent", name="Sent"),
                    NodeParameterOption(value="drafts", name="Drafts"),
                    NodeParameterOption(value="spam", name="Spam"),
                    NodeParameterOption(value="trash", name="Trash"),
                    NodeParameterOption(value="important", name="Important"),
                    NodeParameterOption(value="starred", name="Starred")
                ],
                display_options=DisplayOptions(
                    show={"operation": ["move_email"]}
                ),
            ),

            # Label Management Parameters
            NodeParameter(
                name="label_name",
                type=PropertyTypes.STRING,
                description="Name of the label to create",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["create_label"]}
                ),
            ),
            NodeParameter(
                name="label_list_visibility",
                type=PropertyTypes.OPTIONS,
                description="Label visibility in label list",
                required=False,
                default="labelShow",
                options=[
                    NodeParameterOption(value="inbox", name="Show"),
                    NodeParameterOption(value="labelHide", name="Hide"),
                ],
                display_options=DisplayOptions(
                    show={"operation": ["create_label"]}
                ),
            ),
            NodeParameter(
                name="message_list_visibility",
                type=PropertyTypes.OPTIONS,
                description="Label visibility in message list",
                required=False,
                default="show",
                options=[
                    NodeParameterOption(value="show", name="Show"),
                    NodeParameterOption(value="hide", name="Hide"),
                ],
                display_options=DisplayOptions(
                    show={"operation": ["create_label"]}
                ),
            ),
            NodeParameter(
                name="background_color",
                type=PropertyTypes.STRING,
                description="Label background color (hex format, e.g., #ff0000)",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["create_label"]},
                ),
            ),
            NodeParameter(
                name="text_color",
                type=PropertyTypes.STRING,
                description="Label text color (hex format, e.g., #ffffff)",
                required=False,
                display_options=DisplayOptions(
                    show={"operation": ["create_label"]}
                ),
            ),
            
            # List Labels Parameters
            NodeParameter(
                name="include_system",
                type=PropertyTypes.BOOLEAN,
                description="Include system labels (INBOX, SENT, etc.)",
                required=False,
                default=True,
                display_options=DisplayOptions(
                    show={"operation": ["list_labels"]}
                )
            ),
            NodeParameter(
                name="include_user",
                type=PropertyTypes.BOOLEAN,
                description="Include user-created labels",
                required=False,
                default=True,
                display_options=DisplayOptions(
                    show={"operation": ["list_labels"]}
                )
            ),

            # Label Operations Parameters
            NodeParameter(
                name="label_ids",
                type=PropertyTypes.STRING,
                description="Label IDs (comma-separated)",
                required=False,
                display_options=DisplayOptions(
                    show={
                        "operation": [
                            "add_label",
                            "remove_label"
                        ]
                    },
                ),
            ),
        ]
    
    @staticmethod
    def _get_credential_description() -> NodeCredentialDescription:
        """Get Gmail credential description."""
        return NodeCredentialDescription(
            name="gmail",
            display_name="Gmail",
        )
