"""
Gmail Node Implementation

This module implements the Gmail node for email operations including
sending emails, reading emails, managing labels, and email organization.
"""

import logging
from typing import Dict, Any, Callable

from app.node.node_base.node import Node, NodeResult
from app.node.node_base.node_models import (
    NodeData, 
    NodeRequest, 
    NodeTypeDescription, 
    ValidationResult,
    ValidationError
)
from app.node.node_utils.workflow_defn import node_defn
from app.node.nodes.gmail.gmail_model import GmailNodeDescription
from app.node.nodes.gmail.operations import (
    send_email,
    read_emails,
    mark_read,
    mark_unread,
    delete_email,
    move_email,
    create_label,
    list_labels,
    add_label,
    remove_label
)

# Set up logging
logger = logging.getLogger(__name__)


@node_defn(type='gmail', is_activity=True)
class GmailNode(Node):
    """
    Gmail Node
    
    Provides comprehensive Gmail integration for email operations including:
    - Send emails with attachments and formatting
    - Read emails with filtering and pagination
    - Email management (mark read/unread, delete, move)
    - Label management (create, list, add, remove)
    - Proper OAuth2 authentication and error handling
    """
    
    @classmethod
    def get_description(cls) -> NodeTypeDescription:
        """Get the node type description."""
        return GmailNodeDescription.create()
    
    def __init__(self):
        """Initialize Gmail node with operation handlers."""
        super().__init__()
        
        # Map operations to their corresponding handler functions
        self.operation_handlers: Dict[str, Callable] = {
            # Email operations
            'send_email': send_email.SendEmailOperation.execute,
            'read_emails': read_emails.ReadEmailsOperation.execute,
            
            # Email management operations
            'mark_read': mark_read.MarkReadOperation.execute,
            'mark_unread': mark_unread.MarkUnreadOperation.execute,
            'delete_email': delete_email.DeleteEmailOperation.execute,
            'move_email': move_email.MoveEmailOperation.execute,
            
            # Label management operations
            'create_label': create_label.CreateLabelOperation.execute,
            'list_labels': list_labels.ListLabelsOperation.execute,
            'add_label': add_label.AddLabelOperation.execute,
            'remove_label': remove_label.RemoveLabelOperation.execute
        }

    async def run(self, data: NodeData) -> NodeResult:
        """
        Execute the Gmail node's operations.
        
        Args:
            data: Node execution data containing parameters and credentials
            
        Returns:
            NodeResult: Result of the Gmail operation or error information
        """
        try:
            # Validate input data
            if not data or not data.parameters:
                return NodeResult(error="Invalid node data: parameters are required")
            
            # Get operation type
            operation = data.parameters.get('operation', 'send_email')
            if not isinstance(operation, str):
                return NodeResult(error="Operation must be a string")
            
            # Validate credentials
            if not data.credentials or 'gmail' not in data.credentials:
                return NodeResult(error="Gmail API credentials are required")
            
            # Get operation handler
            handler = self.operation_handlers.get(operation)
            if not handler:
                available_operations = list(self.operation_handlers.keys())
                return NodeResult(error=f"Unsupported operation: {operation}. Available operations: {', '.join(available_operations)}")
            
            # Execute the operation
            logger.info(f"Executing Gmail {operation} operation")
            result = await handler(data)
            
            logger.info(f"Gmail {operation} operation completed successfully")
            return result
            
        except Exception as e:
            error_msg = f"Gmail node execution failed: {str(e)}"
            logger.error(error_msg)
            return NodeResult(error=error_msg)
    
    def validate(self, request: NodeRequest) -> ValidationResult:
        """
        Validate Gmail node configuration.
        
        Args:
            request: Node configuration request
            
        Returns:
            ValidationResult: Validation result with any errors
        """
        errors = []
        
        try:
            # Get operation type
            operation = request.parameters.get('operation', 'send_email')
            
            # Validate operation exists
            if operation not in self.operation_handlers:
                available_operations = list(self.operation_handlers.keys())
                errors.append(ValidationError(
                    parameter="operation",
                    message=f"Unsupported operation: {operation}. Available operations: {', '.join(available_operations)}"
                ))
            
            # Operation-specific validation
            if operation == 'send_email':
                errors.extend(self._validate_send_email(request.parameters))
            elif operation == 'read_emails':
                errors.extend(self._validate_read_emails(request.parameters))
            elif operation in ['mark_read', 'mark_unread', 'delete_email']:
                errors.extend(self._validate_message_operation(request.parameters))
            elif operation == 'move_email':
                errors.extend(self._validate_move_email(request.parameters))
            elif operation == 'create_label':
                errors.extend(self._validate_create_label(request.parameters))
            elif operation in ['add_label', 'remove_label']:
                errors.extend(self._validate_label_operation(request.parameters))
            
        except Exception as e:
            errors.append(ValidationError(
                parameter="general",
                message=f"Validation error: {str(e)}"
            ))
        
        return ValidationResult(valid=len(errors) == 0, errors=errors)
    
    def _validate_send_email(self, parameters: Dict[str, Any]) -> list[ValidationError]:
        """Validate send email operation parameters."""
        errors = []
        
        to_emails = parameters.get('to')
        if not to_emails:
            errors.append(ValidationError(
                parameter="to",
                message="To email addresses are required for send email operation"
            ))
        
        subject = parameters.get('subject', '')
        body_text = parameters.get('body_text', '')
        body_html = parameters.get('body_html', '')
        
        if not subject and not body_text and not body_html:
            errors.append(ValidationError(
                parameter="content",
                message="Subject or body content is required for send email operation"
            ))
        
        return errors
    
    def _validate_read_emails(self, parameters: Dict[str, Any]) -> list[ValidationError]:
        """Validate read emails operation parameters."""
        errors = []
        
        max_results = parameters.get('max_results', 10)
        if not isinstance(max_results, int) or max_results < 1 or max_results > 500:
            errors.append(ValidationError(
                parameter="max_results",
                message="max_results must be an integer between 1 and 500"
            ))
        
        format_type = parameters.get('format', 'metadata')
        if format_type not in ['metadata', 'minimal', 'full']:
            errors.append(ValidationError(
                parameter="format",
                message="format must be one of: metadata, minimal, full"
            ))
        
        return errors
    
    def _validate_message_operation(self, parameters: Dict[str, Any]) -> list[ValidationError]:
        """Validate message-based operations (mark read/unread, delete)."""
        errors = []
        
        message_ids = parameters.get('message_ids')
        if not message_ids:
            errors.append(ValidationError(
                parameter="message_ids",
                message="Message IDs are required for this operation"
            ))
        
        return errors
    
    def _validate_move_email(self, parameters: Dict[str, Any]) -> list[ValidationError]:
        """Validate move email operation parameters."""
        errors = []
        
        message_ids = parameters.get('message_ids')
        if not message_ids:
            errors.append(ValidationError(
                parameter="message_ids",
                message="Message IDs are required for move email operation"
            ))
        
        add_labels = parameters.get('add_labels', [])
        remove_labels = parameters.get('remove_labels', [])
        destination_folder = parameters.get('destination_folder', '')
        
        if not add_labels and not remove_labels and not destination_folder:
            errors.append(ValidationError(
                parameter="labels",
                message="At least one of add_labels, remove_labels, or destination_folder must be specified"
            ))
        
        return errors
    
    def _validate_create_label(self, parameters: Dict[str, Any]) -> list[ValidationError]:
        """Validate create label operation parameters."""
        errors = []
        
        label_name = parameters.get('label_name', '').strip()
        if not label_name:
            errors.append(ValidationError(
                parameter="label_name",
                message="Label name is required for create label operation"
            ))
        
        return errors
    
    def _validate_label_operation(self, parameters: Dict[str, Any]) -> list[ValidationError]:
        """Validate label operations (add/remove label)."""
        errors = []
        
        message_ids = parameters.get('message_ids')
        if not message_ids:
            errors.append(ValidationError(
                parameter="message_ids",
                message="Message IDs are required for label operations"
            ))
        
        label_ids = parameters.get('label_ids')
        if not label_ids:
            errors.append(ValidationError(
                parameter="label_ids",
                message="Label IDs are required for label operations"
            ))
        
        return errors
